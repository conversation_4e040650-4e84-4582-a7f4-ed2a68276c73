/**
 * Script test SMS system với mã hóa Base64
 */

const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('../dist/app.module');

async function testSmsSystemWithEncryption() {
  console.log('=== Test SMS System với Mã Hóa Base64 ===\n');

  try {
    // Khởi tạo NestJS application
    const app = await NestFactory.create(AppModule, { logger: false });
    
    // Lấy SMS system service
    const smsSystemService = app.get('SmsSystemService');
    const smsEncryptionService = app.get('SmsEncryptionService');
    
    console.log('✅ Đã khởi tạo SMS System services thành công\n');

    // Test 1: Kiểm tra mã hóa Base64
    console.log('--- Test 1: Mã Hóa Base64 ---');
    const originalMessage = 'Mã xác thực của bạn là: 123456';
    console.log(`Nội dung gốc: ${originalMessage}`);
    
    const encryptedMessage = smsEncryptionService.encryptSmsContent(originalMessage);
    console.log(`Nội dung mã hóa: ${encryptedMessage}`);
    
    const decryptedMessage = smsEncryptionService.decryptSmsContent(encryptedMessage);
    console.log(`Nội dung giải mã: ${decryptedMessage}`);
    
    const isCorrect = originalMessage === decryptedMessage;
    console.log(`Kết quả: ${isCorrect ? '✅ ĐÚNG' : '❌ SAI'}\n`);

    // Test 2: Kiểm tra mã hóa OTP
    console.log('--- Test 2: Mã Hóa OTP ---');
    const otpContent = 'RedAI OTP: 789012. Không chia sẻ với ai!';
    const userId = 12345;
    
    console.log(`Nội dung OTP: ${otpContent}`);
    console.log(`User ID: ${userId}`);
    
    const encryptedOtp = smsEncryptionService.encryptOtpContent(otpContent, userId);
    console.log(`OTP mã hóa: ${encryptedOtp}`);
    
    const decryptedOtp = smsEncryptionService.decryptOtpContent(encryptedOtp, userId);
    console.log(`OTP giải mã: ${decryptedOtp}`);
    
    const otpIsCorrect = otpContent === decryptedOtp;
    console.log(`Kết quả: ${otpIsCorrect ? '✅ ĐÚNG' : '❌ SAI'}\n`);

    // Test 3: Kiểm tra Base64 validation
    console.log('--- Test 3: Kiểm Tra Base64 Validation ---');
    const validBase64 = Buffer.from('Hello World', 'utf8').toString('base64');
    const invalidText = 'This is not base64!';
    
    console.log(`Valid Base64: ${validBase64}`);
    console.log(`Invalid text: ${invalidText}`);
    
    const isValidBase64 = smsEncryptionService.isBase64Encoded(validBase64);
    const isInvalidBase64 = smsEncryptionService.isBase64Encoded(invalidText);
    
    console.log(`Valid Base64 check: ${isValidBase64 ? '✅ ĐÚNG' : '❌ SAI'}`);
    console.log(`Invalid text check: ${!isInvalidBase64 ? '✅ ĐÚNG' : '❌ SAI'}\n`);

    // Test 4: Test gửi SMS job (không thực sự gửi)
    console.log('--- Test 4: Tạo SMS Job ---');
    try {
      const jobId = await smsSystemService.sendOtpSms(
        '+84977682707',
        '123456',
        userId,
        { 
          CUSTOMER_NAME: 'Nguyễn Văn A',
          EXPIRE_TIME: '5 phút'
        }
      );
      
      if (jobId) {
        console.log(`✅ Đã tạo SMS job thành công với ID: ${jobId}`);
      } else {
        console.log('⚠️ Không thể tạo SMS job (có thể do queue không hoạt động)');
      }
    } catch (error) {
      console.log(`❌ Lỗi khi tạo SMS job: ${error.message}`);
    }

    console.log('\n=== Test Hoàn Thành ===');
    
    // Đóng ứng dụng
    await app.close();
    
  } catch (error) {
    console.error(`❌ Lỗi trong quá trình test: ${error.message}`);
    console.error(error.stack);
  }
}

// Chạy test
testSmsSystemWithEncryption()
  .then(() => {
    console.log('Test completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Test failed:', error);
    process.exit(1);
  });
