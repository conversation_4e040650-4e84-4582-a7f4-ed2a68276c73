import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue, JobsOptions } from 'bullmq';
import { QueueName } from '../../queue';
import { SmsSystemJobData } from './dto/sms-system-job.dto';
import { SmsJobName, SmsTypeEnum } from './constants';

/**
 * Cấu hình mặc định cho job
 */
const DEFAULT_JOB_OPTIONS: JobsOptions = {
  attempts: 3,
  backoff: {
    type: 'exponential',
    delay: 2000,
  },
  removeOnComplete: 100,
  removeOnFail: 50,
};

/**
 * Service xử lý SMS hệ thống
 */
@Injectable()
export class SmsSystemService {
  private readonly logger = new Logger(SmsSystemService.name);

  constructor(
    @InjectQueue(QueueName.SMS) private readonly smsQueue: Queue,
  ) {}

  /**
   * Thêm job gửi SMS hệ thống vào queue
   * @param jobData Dữ liệu job SMS hệ thống
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async sendSmsSystemJob(
    jobData: Omit<SmsSystemJobData, 'timestamp'>,
    opts?: JobsOptions,
  ): Promise<string | undefined> {
    try {
      const smsJobData: SmsSystemJobData = {
        ...jobData,
        timestamp: Date.now(),
      };

      const job = await this.smsQueue.add(SmsJobName.SMS_SYSTEM, smsJobData, {
        ...DEFAULT_JOB_OPTIONS,
        ...opts,
      });

      this.logger.log(
        `Đã thêm job SMS hệ thống vào queue: ${job.id} - Type: ${jobData.type}${
          jobData.userId ? ` cho user ${jobData.userId}` : ''
        }`,
      );
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job SMS hệ thống vào queue: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Gửi SMS OTP
   * @param phone Số điện thoại
   * @param otpCode Mã OTP
   * @param userId ID người dùng (tùy chọn)
   * @param additionalData Dữ liệu bổ sung
   * @returns Promise với ID của job đã tạo
   */
  async sendOtpSms(
    phone: string,
    otpCode: string,
    userId?: number,
    additionalData?: Record<string, any>,
  ): Promise<string | undefined> {
    return this.sendSmsSystemJob({
      phone,
      message: '', // Sẽ được thay thế bởi template
      userId,
      type: SmsTypeEnum.OTP,
      data: {
        OTP_CODE: otpCode,
        ...additionalData,
      },
    });
  }

  /**
   * Gửi SMS thông báo đăng ký
   * @param phone Số điện thoại
   * @param userName Tên người dùng
   * @param userId ID người dùng (tùy chọn)
   * @param additionalData Dữ liệu bổ sung
   * @returns Promise với ID của job đã tạo
   */
  async sendRegistrationSms(
    phone: string,
    userName: string,
    userId?: number,
    additionalData?: Record<string, any>,
  ): Promise<string | undefined> {
    return this.sendSmsSystemJob({
      phone,
      message: '', // Sẽ được thay thế bởi template
      userId,
      type: SmsTypeEnum.REGISTRATION,
      data: {
        USER_NAME: userName,
        ...additionalData,
      },
    });
  }

  /**
   * Gửi SMS thông báo quên mật khẩu
   * @param phone Số điện thoại
   * @param resetCode Mã reset mật khẩu
   * @param userId ID người dùng (tùy chọn)
   * @param additionalData Dữ liệu bổ sung
   * @returns Promise với ID của job đã tạo
   */
  async sendForgotPasswordSms(
    phone: string,
    resetCode: string,
    userId?: number,
    additionalData?: Record<string, any>,
  ): Promise<string | undefined> {
    return this.sendSmsSystemJob({
      phone,
      message: '', // Sẽ được thay thế bởi template
      userId,
      type: SmsTypeEnum.FORGOT_PASSWORD,
      data: {
        RESET_CODE: resetCode,
        ...additionalData,
      },
    });
  }
}
