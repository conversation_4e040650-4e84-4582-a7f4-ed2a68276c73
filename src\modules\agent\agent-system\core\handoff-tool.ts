import { z } from 'zod';
import { tool } from '@langchain/core/tools';
import { HumanMessage, ToolMessage } from '@langchain/core/messages';
import { Command, getCurrentTaskInput } from '@langchain/langgraph';
import { AgentState } from './react-agent-executor';
import { DynamicStructuredTool } from '@langchain/core/dist/tools';
import { Logger } from '@nestjs/common';

const logger = new Logger('HandoffTool');
// Factory to create Zod schema based on available worker IDs
const createHandoffSchema = (agentIds: string[]) =>
  z.object({
    agentId: z
      .string()
      .nonempty()
      .refine((id) => agentIds.includes(id), { message: 'Invalid agentId' })
      .describe(`ID of the worker agent to handoff to, must be of the following values: 
${agentIds.join('\n')}`),
    taskDescription: z
      .string()
      .nonempty()
      .describe('Optional description for the worker worker'),
  });

export function createHandoffTool(
  workerAgentIds: string[],
): DynamicStructuredTool {
  if (workerAgentIds.length === 0) {
    throw new Error('createHandoffTool requires at least one workerAgentId');
  }

  const schema = createHandoffSchema(workerAgentIds);
  const toolName = 'handoff_to_worker_agents';

  const handler = async (args: z.infer<typeof schema>, config: any) => {
    const state = getCurrentTaskInput() as AgentState;
    const { agentId, taskDescription } = args;
    const toolMessage = new ToolMessage({
      content: `Successfully transferred to ${agentId}`,
      name: toolName,
      tool_call_id: config.toolCall.id,
      response_metadata: {
        invoker: state.activeAgent,
      },
    });


    const aiMessage = new HumanMessage({
      content: `this task is passed down from your supervisor. Complete the task:
<task-from-supervisor>${taskDescription}</task-from-supervisor>
`,
      response_metadata: {
        invoker: agentId,
      },
    });


    return new Command({
      goto: 'worker',
      graph: Command.PARENT,
      update: {
        messages: state.messages.concat(toolMessage, aiMessage),
        activeAgent: agentId,
        isSupervisor: false,
      },
    });
  };

  return tool(handler, {
    name: toolName,
    description: 'Handoff the task to a worker worker',
    schema,
  });
}
