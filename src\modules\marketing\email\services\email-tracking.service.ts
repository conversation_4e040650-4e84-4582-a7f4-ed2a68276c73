import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RedisService } from '../../../../infra';
import { UserCampaignHistory } from '../../entities/user-campaign-history.entity';
import { EmailTrackingDto } from '../dto';

/**
 * Service xử lý tracking email
 */
@Injectable()
export class EmailTrackingService {
  private readonly logger = new Logger(EmailTrackingService.name);
  private readonly REDIS_TRACKING_KEY = 'email_tracking';
  private readonly BATCH_SIZE = 100;
  private readonly BATCH_INTERVAL = 30000; // 30 seconds

  constructor(
    @InjectRepository(UserCampaignHistory)
    private readonly campaignHistoryRepository: Repository<UserCampaignHistory>,
    private readonly redisService: RedisService,
  ) {
    // Khởi động batch processor
    this.startBatchProcessor();
  }

  /**
   * Lưu tracking event vào Redis
   * @param trackingData Dữ liệu tracking
   */
  async saveTrackingToRedis(trackingData: EmailTrackingDto): Promise<void> {
    try {
      const redisKey = `${this.REDIS_TRACKING_KEY}:${Date.now()}:${Math.random()}`;
      const data = JSON.stringify(trackingData);

      await this.redisService.getRawClient().setex(redisKey, 3600, data); // TTL 1 hour

      this.logger.debug(
        `Tracking data saved to Redis: ${trackingData.trackingId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error saving tracking to Redis: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Tạo tracking ID duy nhất
   * @param campaignId ID campaign
   * @param audienceId ID audience
   * @returns Tracking ID
   */
  generateTrackingId(campaignId: number, audienceId: number): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2);
    return `${campaignId}_${audienceId}_${timestamp}_${random}`;
  }

  /**
   * Xử lý tracking event khi email được gửi
   * @param campaignId ID campaign
   * @param audienceId ID audience
   * @param email Email người nhận
   * @param trackingId ID tracking
   */
  async trackEmailSent(
    campaignId: number,
    audienceId: number,
    email: string,
    trackingId: string,
  ): Promise<void> {
    const trackingData: EmailTrackingDto = {
      trackingId,
      campaignId,
      audienceId,
      email,
      eventType: 'sent',
      timestamp: Date.now(),
    };

    await this.saveTrackingToRedis(trackingData);
  }

  /**
   * Xử lý tracking event khi email được mở (pixel tracking)
   * @param trackingId ID tracking
   * @param metadata Thông tin bổ sung (IP, User-Agent, etc.)
   */
  async trackEmailOpened(trackingId: string, metadata?: any): Promise<void> {
    try {
      // Lấy thông tin campaign và audience từ tracking ID
      const trackingInfo = this.parseTrackingId(trackingId);
      if (!trackingInfo) {
        this.logger.warn(`Invalid tracking ID: ${trackingId}`);
        return;
      }

      const trackingData: EmailTrackingDto = {
        trackingId,
        campaignId: trackingInfo.campaignId,
        audienceId: trackingInfo.audienceId,
        email: '', // Sẽ được lấy từ database nếu cần
        eventType: 'opened',
        timestamp: Date.now(),
        metadata,
      };

      await this.saveTrackingToRedis(trackingData);
    } catch (error) {
      this.logger.error(
        `Error tracking email opened: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Xử lý tracking event khi email gửi thất bại
   * @param campaignId ID campaign
   * @param audienceId ID audience
   * @param email Email người nhận
   * @param trackingId ID tracking
   * @param error Thông tin lỗi
   */
  async trackEmailFailed(
    campaignId: number,
    audienceId: number,
    email: string,
    trackingId: string,
    error?: any,
  ): Promise<void> {
    const trackingData: EmailTrackingDto = {
      trackingId,
      campaignId,
      audienceId,
      email,
      eventType: 'failed',
      timestamp: Date.now(),
      metadata: { error: error?.message || 'Unknown error' },
    };

    await this.saveTrackingToRedis(trackingData);
  }

  /**
   * Parse tracking ID để lấy thông tin campaign và audience
   * @param trackingId ID tracking
   * @returns Thông tin parsed hoặc null
   */
  private parseTrackingId(
    trackingId: string,
  ): { campaignId: number; audienceId: number } | null {
    try {
      const parts = trackingId.split('_');
      if (parts.length >= 2) {
        return {
          campaignId: parseInt(parts[0]),
          audienceId: parseInt(parts[1]),
        };
      }
      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Khởi động batch processor để lưu dữ liệu từ Redis vào database
   */
  private startBatchProcessor(): void {
    setInterval(async () => {
      await this.processBatchTracking();
    }, this.BATCH_INTERVAL);

    this.logger.log('Email tracking batch processor started');
  }

  /**
   * Xử lý batch tracking data từ Redis vào database
   */
  private async processBatchTracking(): Promise<void> {
    try {
      const redis = this.redisService.getRawClient();
      const pattern = `${this.REDIS_TRACKING_KEY}:*`;
      const keys = await redis.keys(pattern);

      if (keys.length === 0) {
        return;
      }

      // Lấy dữ liệu từ Redis
      const trackingEvents: EmailTrackingDto[] = [];
      const pipeline = redis.pipeline();

      for (const key of keys.slice(0, this.BATCH_SIZE)) {
        pipeline.get(key);
        pipeline.del(key); // Xóa key sau khi lấy
      }

      const results = await pipeline.exec();

      // Parse dữ liệu
      if (results && results.length > 0) {
        for (let i = 0; i < results.length; i += 2) {
          const [getError, data] = results[i];
          if (!getError && data) {
            try {
              const trackingEvent = JSON.parse(data as string);
              trackingEvents.push(trackingEvent);
            } catch (parseError) {
              this.logger.error(
                `Error parsing tracking data: ${parseError.message}`,
              );
            }
          }
        }
      }

      if (trackingEvents.length > 0) {
        await this.saveBatchToDatabase(trackingEvents);
        this.logger.debug(`Processed ${trackingEvents.length} tracking events`);
      }
    } catch (error) {
      this.logger.error(
        `Error processing batch tracking: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Lưu batch tracking events vào database
   * @param events Danh sách tracking events
   */
  private async saveBatchToDatabase(events: EmailTrackingDto[]): Promise<void> {
    try {
      const historyEntries = events.map((event) => {
        const history = new UserCampaignHistory();
        history.campaignId = event.campaignId;
        history.audienceId = event.audienceId;
        history.status = event.eventType;
        history.sentAt = event.timestamp;
        history.createdAt = Date.now();
        return history;
      });

      await this.campaignHistoryRepository.save(historyEntries);
      this.logger.debug(
        `Saved ${historyEntries.length} tracking events to database`,
      );
    } catch (error) {
      this.logger.error(
        `Error saving batch to database: ${error.message}`,
        error.stack,
      );
    }
  }
}
