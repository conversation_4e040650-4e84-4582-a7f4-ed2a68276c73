# Batch Email Marketing Processor Guide

## Tổng quan

Worker processor đã được cập nhật để hỗ trợ xử lý batch email marketing jobs với template từ database. Processor sẽ tự động:

1. Lấy template từ database bằng templateId
2. <PERSON><PERSON><PERSON> custom fields của từng audience
3. <PERSON><PERSON><PERSON> hợp template variables với custom fields
4. G<PERSON>i email cho tất cả recipients

**Lưu ý**: Worker chỉ xử lý jobs, không có API để tạo jobs. Jobs được tạo từ main application và đẩy vào queue.

## Queue Job Types

### 1. Single Email Job (`send-email`)
Xử lý email đơn lẻ như trước

### 2. Batch Email Job (`send-batch-email`)
Xử lý nhiều email cùng lúc với template từ database

## API Endpoints (Chỉ để monitoring)

### 1. Kiểm tra Queue Status

**GET** `/api/email-marketing/queue/status`

**Response:**
```json
{
  "success": true,
  "data": {
    "waiting": 5,
    "active": 2,
    "completed": 100,
    "failed": 3,
    "delayed": 10
  }
}
```

## Template Variables Logic

### Global Template Variables
Được định nghĩa trong `templateVariables` và áp dụng cho tất cả recipients:
```json
{
  "companyName": "Your Company",
  "promotionCode": "SAVE20"
}
```

### Audience Custom Fields
Được lấy từ database cho từng audience riêng biệt:
```json
{
  "name": "John Doe",
  "city": "Hanoi",
  "lastPurchase": "2024-01-15"
}
```

### Combined Variables
Processor sẽ kết hợp cả hai:
```json
{
  "companyName": "Your Company",    // From templateVariables
  "promotionCode": "SAVE20",        // From templateVariables
  "name": "John Doe",               // From audience custom fields
  "city": "Hanoi",                  // From audience custom fields
  "lastPurchase": "2024-01-15"      // From audience custom fields
}
```

## Template Example

**Template trong database:**
```html
Subject: {{companyName}} - Special offer for {{name}}!

Content:
<h1>Hello {{name}}!</h1>
<p>We have a special offer for customers in {{city}}.</p>
<p>Use code <strong>{{promotionCode}}</strong> to get 20% off!</p>
<p>Don't miss out - offer expires soon!</p>
<p>Best regards,<br>{{companyName}} Team</p>
```

**Kết quả sau khi process:**
```html
Subject: Your Company - Special offer for John Doe!

Content:
<h1>Hello John Doe!</h1>
<p>We have a special offer for customers in Hanoi.</p>
<p>Use code <strong>SAVE20</strong> to get 20% off!</p>
<p>Don't miss out - offer expires soon!</p>
<p>Best regards,<br>Your Company Team</p>
```

## Server Configuration

### Option 1: Sử dụng server config từ request
```json
{
  "server": {
    "host": "smtp.gmail.com",
    "port": 587,
    "secure": false,
    "user": "<EMAIL>",
    "password": "your-password",
    "from": "<EMAIL>"
  }
}
```

### Option 2: Không truyền server config (sử dụng env default)
```json
{
  // Không có field "server"
  // Processor sẽ sử dụng config từ environment variables:
  // MAIL_HOST, MAIL_PORT, MAIL_USERNAME, MAIL_PASSWORD, etc.
}
```

## Error Handling

### Validation Errors
- Missing campaignId, templateId, hoặc audienceIds
- Template không tồn tại
- Không có audience nào có email hợp lệ

### Processing Errors
- Lỗi kết nối SMTP
- Lỗi gửi email cho một recipient cụ thể
- Lỗi lấy custom fields

### Retry Mechanism
- Jobs sẽ được retry 3 lần với exponential backoff
- Failed emails được track trong database
- Batch job sẽ tiếp tục xử lý các recipients khác nếu một email fail

## Monitoring

### Queue Dashboard
Truy cập `/queues` để xem:
- Jobs đang chờ, đang xử lý, hoàn thành, failed
- Progress của batch jobs
- Error logs

### Logs
```
[EmailMarketingProcessor] Processing batch email job: 12345 for campaign 1 with 4 recipients
[EmailMarketingProcessor] Email sent successfully: <message-id> to <EMAIL>
[EmailMarketingProcessor] Batch email job 12345 completed: 3 sent, 1 failed
```

## Testing

### 1. Tạo template trong database
```sql
INSERT INTO user_template_email (user_id, name, subject, content, created_at, updated_at) 
VALUES (1, 'Welcome Email', 'Welcome {{name}}!', '<h1>Hello {{name}}</h1>', 1640*********, 1640*********);
```

### 2. Tạo audiences với custom fields
```sql
INSERT INTO user_audience (id, user_id, email, created_at, updated_at) 
VALUES (123, 1, '<EMAIL>', 1640*********, 1640*********);

INSERT INTO user_audience_custom_fields (audience_id, field_name, field_value, created_at, updated_at) 
VALUES (123, 'name', '"John Doe"', 1640*********, 1640*********);
```

### 3. Tạo Job từ Main Application
```typescript
// Từ main application, thêm job vào queue
import { Queue } from 'bullmq';
import { EmailMarketingJobName, BatchEmailMarketingJobDto } from './dto';

const jobData: BatchEmailMarketingJobDto = {
  campaignId: 1,
  templateId: 1,
  templateVariables: { companyName: "Test Company" },
  recipients: [
    { audienceId: 123, email: "<EMAIL>" }
  ],
  createdAt: Date.now()
};

await emailMarketingQueue.add(EmailMarketingJobName.SEND_BATCH_EMAIL, jobData);
```
