# SMS System Implementation - RedAI Worker

## Tổng quan

Đã triển khai thành công hệ thống SMS worker cho RedAI với các tính năng:

✅ **Queue SMS**: Thêm queue SMS vào hệ thống Bull  
✅ **Template Engine**: <PERSON>u<PERSON><PERSON> lý template SMS từ database với biến động  
✅ **FPT SMS Integration**: <PERSON><PERSON><PERSON> hợp FPT SMS Brandname service  
✅ **Job Processing**: Xử lý job SMS bất đồng bộ  
✅ **Error Handling**: Retry logic và logging chi tiết  

## Cấu trúc Implementation

### 1. Queue Configuration
- **File**: `src/queue/queue-name.enum.ts`, `src/queue/queue.module.ts`
- **Thêm**: `QueueName.SMS = 'sms'`
- **Bull Dashboard**: Hiển thị queue SMS tại `/queues`

### 2. SMS System Module
- **Location**: `src/modules/sms_system/`
- **Components**:
  - `SmsSystemService`: Service chính để tạo job
  - `SmsSystemProcessor`: Xử lý job từ queue
  - `SmsTemplateService`: Quản lý template và thay thế biến
  - `AdminTemplateSms`: Entity cho bảng template SMS

### 3. Constants & Types
- **SmsTypeEnum**: 12 loại SMS (OTP, REGISTRATION, LOGIN, etc.)
- **SmsJobName**: Tên job trong queue
- **SmsSystemJobData**: Interface cho job data

### 4. Database Integration
- **Table**: `admin_template_sms`
- **Fields**: id, category, content, placeholders, timestamps
- **Seed Script**: Tạo 12 template mẫu

### 5. FPT SMS Integration
- **Service**: `FprSmsBrandnameService`
- **Config**: Sử dụng biến môi trường FPT_SMS_*
- **Method**: `sendOtp()` để gửi SMS brandname

## Luồng xử lý

```
App chính → SmsSystemService.sendSmsSystemJob()
    ↓
Queue SMS (Bull/Redis)
    ↓
SmsSystemProcessor.process()
    ↓
SmsTemplateService.processTemplate()
    ↓ (query database)
admin_template_sms table
    ↓ (replace variables)
Template với {{BIEN}} → Nội dung cuối
    ↓
FprSmsBrandnameService.sendOtp()
    ↓
FPT SMS API
```

## Cách sử dụng

### 1. Từ app chính (backend)
```typescript
// Inject service
constructor(private readonly smsSystemService: SmsSystemService) {}

// Gửi SMS OTP
await this.smsSystemService.sendOtpSms(
  '0912345678',
  '123456',
  userId,
  { USER_NAME: 'Nguyễn Văn A' }
);
```

### 2. Template SMS
```sql
-- Template trong database
content: "Ma xac thuc cua ban la: {{OTP_CODE}}. Vui long khong chia se ma nay."

-- Sau khi thay thế
"Ma xac thuc cua ban la: 123456. Vui long khong chia se ma nay."
```

### 3. Job Data Structure
```typescript
{
  phone: '0912345678',
  message: '', // Sẽ được thay thế bởi template
  userId: 1,
  type: SmsTypeEnum.OTP,
  data: {
    OTP_CODE: '123456',
    USER_NAME: 'Nguyễn Văn A'
  },
  timestamp: 1703123456789
}
```

## Files đã tạo/sửa

### Tạo mới:
- `src/modules/sms_system/` (toàn bộ module)
- `scripts/test-sms-system.js`
- `SMS_SYSTEM_IMPLEMENTATION.md`

### Sửa đổi:
- `src/queue/queue-name.enum.ts`: Thêm SMS queue
- `src/queue/queue.module.ts`: Register SMS queue
- `src/app.module.ts`: Import SmsSystemModule
- `src/shared/services/sms/fpr-sms-brandname.service.ts`: Thêm Status, ErrorMessage

## Testing

### 1. Build project
```bash
npm run build
```

### 2. Seed templates
```bash
npx ts-node src/modules/sms_system/scripts/seed-sms-templates.ts
```

### 3. Test SMS system
```bash
npx ts-node src/modules/sms_system/test-sms.ts
```

### 4. All-in-one test
```bash
node scripts/test-sms-system.js
```

## Environment Variables

```env
# FPT SMS Configuration (đã có)
FPT_SMS_CLIENT_ID=888a1ad38442ea0d8dcc6B75f843b32FD5d10c88
FPT_SMS_CLIENT_SECRET=f13F32a055082062d9c508ce81292880EF4987e4763e2D55509073872e8423dC4a6ca42d
FPT_SMS_SCOPE=send_brandname_otp send_brandname
FPT_SMS_API_URL=http://api.fpt.net/api
FPT_SMS_BRANDNAME=REDAI
```

## Monitoring

- **Queue Dashboard**: `http://localhost:3000/queues`
- **Credentials**: admin / redai@123
- **Logs**: Console logs với chi tiết job processing

## Lưu ý quan trọng

1. **Database**: Cần tạo bảng `admin_template_sms` trước khi chạy
2. **Templates**: Phải seed templates trước khi gửi SMS
3. **FPT SMS**: Cần credentials hợp lệ để gửi SMS thực
4. **Variables**: Biến trong template phải match với data truyền vào
5. **Retry**: Job sẽ retry 3 lần nếu gặp lỗi

## Next Steps

1. **Production**: Deploy và test với FPT SMS credentials thực
2. **Monitoring**: Thêm metrics và alerting
3. **Templates**: Tạo UI để quản lý templates
4. **Validation**: Thêm validation cho phone numbers
5. **Rate Limiting**: Thêm rate limiting cho SMS sending
