import { Injectable } from '@nestjs/common';
import { BaseLangGraphEventHandler } from './base-event-handler';
import { EventProcessingContext } from '../schemas';
import { TOOL_CALL_TAGS } from '../core/constants';

/**
 * Handler for tool-related events from LangGraph
 * Handles on_tool_start and on_tool_end events
 */
@Injectable()
export class ToolEventsHandler extends BaseLangGraphEventHandler {
  /**
   * Check if this handler can process the given event
   * @param event - LangGraph event type
   * @param data - Event data
   * @param tags - Event tags
   * @returns True if this is a tool start or end event
   */
  canHandle(event: string, data: any, tags: string[]): boolean {
    return ['on_tool_start', 'on_tool_end'].includes(event);
  }

  /**
   * Process tool start/end events
   * Emits tool_call_start or tool_call_end events with role and tool information
   * @param context - Event processing context
   */
  async handle(context: EventProcessingContext): Promise<void> {
    const role = this.getRoleFromTags(context.tags, TOOL_CALL_TAGS)!;
    const eventType = context.event === 'on_tool_start' ? 'tool_call_start' : 'tool_call_end';

    // Enhanced tool logging with tool name if available (from original)
    let toolInfo = '';
    let toolName: string | undefined;

    if (context.data && typeof context.data === 'object' && 'name' in context.data) {
      toolName = (context.data as any).name;
      toolInfo = ` (${toolName})`;
    }

    this.logEvent('🔧', `Tool ${eventType} [${role}]${toolInfo}`, context, {
      role,
      toolName,
      eventType,
    });

    await context.emitEventCallback({
      type: eventType as 'tool_call_start' | 'tool_call_end',
      data: { role, toolName },
    });
  }
}
