import { Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { HumanMessage, RemoveMessage } from '@langchain/core/messages';
import { StreamingSetupService as IStreamingSetupService } from '../interfaces/service.interface';
import { StreamingComponents, ThreadConfiguration } from '../schemas';
import { EmitEventCallback } from '../interfaces/event';
import { TokenUsageCollector } from '../token-usage-collector';
import { UserUsageQueries } from './user-usage.queries';
import { MessageContentService } from './message-content.service';
import { CdnService, RedisService } from '../../../../infra';
import { workflow } from '../core';
import { SystemAgentConfigMap } from '../interfaces/agent-system.interface';
import {
  AttachmentContextBlock,
  ContentBlock,
  fileContentBlockToXml,
  imageContentBlockToXml,
  isFileContentBlock,
  isImageContent<PERSON>lock,
  isTextContentBlock,
  textContentBlockToXml,
} from '../interfaces/message.interface';
import { Command } from '@langchain/langgraph';
import { InterruptShapeInterface } from '../../interfaces';
import { TimeIntervalEnum } from '@common/dto/time-interval.enum';
import { HttpService } from '@nestjs/axios';
import { Readable, Transform } from 'stream';
import { firstValueFrom } from 'rxjs';
import { toBeDeleted } from '../../constants/special-message-id';
import { v4 } from 'uuid';
import base64 from 'base64-stream';

/**
 * Service responsible for setting up streaming components and dependencies
 * Extracted from AgentSystemService.processAgentThread method (Section 2: Input Preparation & Section 3: Streaming Initialization)
 */
@Injectable()
export class StreamingSetupService implements IStreamingSetupService {
  private readonly logger = new Logger(StreamingSetupService.name);

  constructor(
    private readonly cdnService: CdnService,
    private readonly redisService: RedisService,
    private readonly userUsageQueries: UserUsageQueries,
    private readonly messageContentService: MessageContentService,
    private readonly httpService: HttpService,
  ) {}

  /**
   * Setup all streaming components required for agent thread processing with everything needed from start
   * @param config - Thread configuration containing all necessary data
   * @param emitEventCallback - The real emit event callback from AgentSystemService
   * @param abortController - Abort controller for cancellation support
   * @returns Complete streaming components ready for event processing
   */
  async setupComponents(
    config: ThreadConfiguration,
    emitEventCallback: EmitEventCallback,
    abortController: AbortController,
  ): Promise<StreamingComponents> {
    try {
      this.logger.debug(
        `Setting up streaming components for thread ${config.threadId}`,
        {
          threadId: config.threadId,
          runId: config.runData.id,
          userId: config.userId,
        },
      );

      // 1. Setup Redis producer client
      const producer = this.redisService.getRawClient();

      // 2. Prepare partial tokens array for accumulation
      const partialTokens: string[] = [];

      // 4. Create TokenUsageCollector with the real callback passed from AgentSystemService
      const tokenUsageCollector = new TokenUsageCollector(
        config.customConfig.agentConfigMap as SystemAgentConfigMap,
        config.userId,
        config.threadId,
        config.runData.id,
        this.userUsageQueries,
        emitEventCallback, // Use the real callback passed as parameter
      );

      // 5. Prepare and validate streaming input
      const streamingInput = await this.prepareStreamingInput(config);

      // 6. Create streaming iterator with TokenUsageCollector and AbortController from start
      const streamingIterator = await this.createStreamingIterator(
        streamingInput,
        config,
        tokenUsageCollector,
        abortController,
      );

      // 7. Create abort listener function
      const abortListener = async () => {
        this.logger.log(`Thread ${config.threadId} processing was cancelled`);
        // Force-close the LLM stream generator
        await streamingIterator?.return?.();
      };

      // 8. Register abort listener
      abortController.signal.addEventListener('abort', abortListener);

      const components: StreamingComponents = {
        producer,
        tokenUsageCollector,
        emitEventCallback, // Real callback passed from AgentSystemService
        streamingInput,
        threadConfig: config, // Include thread config for abort signal recreation
        partialTokens,
        streamingIterator,
        abortListener,
      };

      this.logger.debug(
        `Successfully set up streaming components for thread ${config.threadId}`,
        {
          threadId: config.threadId,
          hasProducer: !!producer,
          hasTokenCollector: !!tokenUsageCollector,
          hasStreamingInput: !!streamingInput,
          hasStreamingIterator: !!streamingIterator,
          hasAbortListener: !!abortListener,
        },
      );

      return components;
    } catch (error) {
      this.logger.error(
        `Failed to setup streaming components for thread ${config.threadId}:`,
        {
          threadId: config.threadId,
          runId: config.runData.id,
          error: error.message,
          stack: error.stack,
        },
      );
      throw error;
    }
  }
  async streamUrlToBase64(url: string): Promise<Base64Result> {
    try {
      // 1. Lấy response, bao gồm stream và headers
      const response = await firstValueFrom(
        this.httpService.get(url, { responseType: 'stream' }),
      );

      const mimeType = response.headers['content-type'] || 'application/octet-stream';
      const fileStream = response.data as Readable;

      // 👇 THAY ĐỔI 2: Sử dụng Encoder từ thư viện thay vì class tự chế
      const base64Encoder = new base64.Base64Encode();

      let accumulatedBase64 = '';

      // 3. Nối stream nguồn (download) vào stream chuyển đổi (encoder)
      const encodedStream = fileStream.pipe(base64Encoder);

      // 4. Dùng vòng lặp 'for await...of' để tiêu thụ stream đã mã hóa
      for await (const chunk of encodedStream) {
        accumulatedBase64 += chunk;
      }

      // 5. Sau khi vòng lặp kết thúc, chuỗi đã hoàn chỉnh
      return {
        base64String: accumulatedBase64,
        mimeType: mimeType,
      };

    } catch (error) {
      this.logger.error(`Failed to stream or convert file from url: ${url}`, error.stack);
      throw new InternalServerErrorException('Failed to process the file stream.');
    }
  }

  /**
   * Prepare and validate streaming input from thread configuration
   * @param config - Thread configuration
   * @returns Validated streaming input for LangGraph
   */
  private async prepareStreamingInput(
    config: ThreadConfiguration,
  ): Promise<any> {
    try {
      // Extract message data
      const messageData = config.messageData;
      const contentBlocks: ContentBlock[] = messageData.contentBlocks || [];
      const attachmentContext: AttachmentContextBlock[] =
        messageData.attachmentContext || [];
      const replyToMessageId: string | undefined = messageData.replyToMessageId;

      // ✅ NEW: Validate content blocks instead of simple string
      if (!contentBlocks || contentBlocks.length === 0) {
        this.logger.error(
          `[prepareStreamingInput]: Empty content blocks for thread ${config.threadId}`,
          {
            threadId: config.threadId,
            hasPayload: !!config.decryptedPayload,
            hasMessage: !!config.decryptedPayload?.message,
            contentBlockCount: contentBlocks?.length || 0,
            attachmentCount: attachmentContext?.length || 0,
            payloadStructure: {
              keys: config.decryptedPayload
                ? Object.keys(config.decryptedPayload)
                : [],
              messageKeys: config.decryptedPayload?.message
                ? Object.keys(config.decryptedPayload.message)
                : [],
            },
            rawPayloadSample: JSON.stringify(config.decryptedPayload).substring(
              0,
              500,
            ),
          },
        );
        throw new Error('Content blocks are empty or invalid');
      }

      let input: any;

      this.logger.debug(
        `contentBlocks = ${JSON.stringify(contentBlocks, null, 2)}`,
      );

      if (contentBlocks[0].type === 'tool_call_decision') {
        this.logger.debug(`tool call decision is ${contentBlocks[0].decision}`);
        const toolCallDecision: InterruptShapeInterface = {
          choice: contentBlocks[0].decision,
        };
        input = new Command({ resume: toolCallDecision });
      } else {
        // Generate message content with reply context
        const messageContent = await this.generateMessageContentFromBlocks(
          contentBlocks,
          attachmentContext || [],
          replyToMessageId,
        );

        // ✅ NEW: Get message ID from run data for future use
        const messageId = config.runData?.payload?.messageId || v4();

        this.logger.debug(`[${this.prepareStreamingInput.name}]: messageId = ${messageId}`);

        // Build input for multi-agent LangGraph workflow with message ID
        const humanMessage = new HumanMessage({
          id: messageId,
          content: [
            {
              type: 'text',
              text: messageContent,
            },
          ],
        });

        const imageHumanMessageContent = await Promise.all(
          contentBlocks
            .filter((block) => block.type === 'image')
            .map(async (block) => {
              const viewUrl = this.cdnService.generateUrlView(
                block.path,
                TimeIntervalEnum.FIVE_MINUTES,
              ) as string;
              // from file stream to base64 string
              const { base64String, mimeType } =
                await this.streamUrlToBase64(viewUrl);
              return {
                type: 'image_url',
                image_url: {
                  url: `data:${mimeType};base64,${base64String}`,
                },
              };
            }),
        );

        const imageHumanMessage = new HumanMessage({
          id: toBeDeleted,
          content: imageHumanMessageContent,
        });


        const deletedMessageIds = config.runData?.modificationDetails?.deletedMessageIds || [];

        const removeMessages = deletedMessageIds.map((id: string) => new RemoveMessage({ id }));

        input = {
          messages: [humanMessage, imageHumanMessage, ...removeMessages],
          activeAgent: config.customConfig.supervisorAgentId || 'supervisor',
        };

        this.logger.debug(
          `Prepared streaming input for thread ${config.threadId}:`,
          {
            threadId: config.threadId,
            activeAgent: input.activeAgent,
            contentBlockCount: contentBlocks.length,
            contentBlockTypes: contentBlocks.map((block: any) => block.type),
            attachmentCount: (attachmentContext || []).length,
            messageLength: messageContent.length,
            messagePreview: messageContent.substring(0, 100),
            alwaysApproveToolCall: config.customConfig.alwaysApproveToolCall,
            humanMessageType: humanMessage.constructor.name,
            hasValidMessage: !!humanMessage.content,
          },
        );
      }
      return input;
    } catch (error) {
      this.logger.error(
        `Failed to prepare streaming input for thread ${config.threadId}:`,
        {
          threadId: config.threadId,
          error: error.message,
          stack: error.stack,
        },
      );
      throw error;
    }
  }

  /**
   * Create streaming iterator with LangGraph workflow with everything needed from start
   * @param input - Streaming input for LangGraph
   * @param config - Thread configuration
   * @param tokenUsageCollector - Token usage collector instance
   * @param abortController - Abort controller for cancellation support
   * @returns Streaming iterator for event processing
   */
  private async createStreamingIterator(
    input: any,
    config: ThreadConfiguration,
    tokenUsageCollector: TokenUsageCollector,
    abortController: AbortController,
  ): Promise<AsyncIterableIterator<any>> {
    try {
      this.logger.debug(
        `Creating streaming iterator for thread ${config.threadId}`,
        {
          threadId: config.threadId,
          inputActiveAgent: input.activeAgent,
          hasTokenCollector: !!tokenUsageCollector,
          hasAbortController: !!abortController,
        },
      );

      const streaming = workflow.streamEvents(input, {
        configurable: config.customConfig,
        subgraphs: true,
        recursionLimit: 500,
        version: 'v2' as const,
        signal: abortController.signal,
        streamMode: ['values', 'updates'],
        callbacks: [tokenUsageCollector],
      });

      const streamingIterator = streaming[Symbol.asyncIterator]();

      this.logger.debug(
        `Successfully created streaming iterator for thread ${config.threadId}`,
        {
          threadId: config.threadId,
          hasIterator: !!streamingIterator,
        },
      );

      return streamingIterator;
    } catch (error) {
      this.logger.error(
        `Failed to create streaming iterator for thread ${config.threadId}:`,
        {
          threadId: config.threadId,
          error: error.message,
          stack: error.stack,
        },
      );
      throw error;
    }
  }

  /**
   * Generate message content with reply context loading
   * @param contentBlocks Array of content blocks
   * @param attachmentContext Array of attachment context
   * @param replyToMessageId Optional message ID to reply to
   * @returns Generated message content string
   */
  private async generateMessageContentFromBlocks(
    contentBlocks: ContentBlock[],
    attachmentContext: AttachmentContextBlock[],
    replyToMessageId?: string,
  ): Promise<string> {
    try {
      let messageContent = '';

      // Add structured message wrapper with metadata
      const timestamp = new Date().toISOString();
      const hasReply = !!replyToMessageId;
      const hasAttachments = !!(
        attachmentContext && attachmentContext.length > 0
      );

      messageContent += `<user-message timestamp="${timestamp}" has-reply="${hasReply}" has-attachments="${hasAttachments}">\n`;

      // Handle reply context at message level
      if (replyToMessageId) {
        messageContent += `  <reply-to>\n`;

        // Load original message content for context
        try {
          const originalMessageText =
            await this.messageContentService.getMessageTextById(
              replyToMessageId,
            );
          if (originalMessageText) {
            messageContent += `    <original-message>${originalMessageText}</original-message>\n`;
          } else {
            messageContent += `    <original-message status="not-found">Message not found</original-message>\n`;
          }
        } catch (error) {
          this.logger.warn(
            `Failed to load original message ${replyToMessageId}:`,
            error,
          );
          messageContent += `    <original-message status="unavailable">Error loading message</original-message>\n`;
        }

        messageContent += '  </reply-to>\n\n';
      }

      // Process content blocks with structured format
      if (contentBlocks && contentBlocks.length > 0) {
        messageContent += '  <message-content>\n';
        for (const block of contentBlocks) {
          if (isTextContentBlock(block)) {
            messageContent += `    ${textContentBlockToXml(block)}\n`;
          }
        }
        messageContent += '  </message-content>\n\n';
      }

      // Process attachment context with structured format
      if (attachmentContext && attachmentContext.length > 0) {
        messageContent += '  <attachment-context>\n';
        for (const attachment of attachmentContext) {
          if (isFileContentBlock(attachment)) {
            messageContent += fileContentBlockToXml(attachment);
          } else if (isImageContentBlock(attachment)) {
            messageContent += imageContentBlockToXml(attachment);
          }
        }
        messageContent += '  </attachment-context>\n';
      }

      // Close structured message wrapper
      messageContent += '</user-message>';

      const returnMessage =
        messageContent.trim() ||
        `<user-message>
  <empty-message>
    No content provided
  </empty-message>
</user-message>`;

      this.logger.debug(`Generated message content from blocks:`, {
        contentLength: returnMessage.length,
        contentPreview: returnMessage,
      });

      return returnMessage;
    } catch (error) {
      this.logger.error('Failed to generate message content from blocks:', {
        error: error.message,
        contentBlockCount: contentBlocks?.length || 0,
        attachmentCount: attachmentContext?.length || 0,
      });
      return '[Error processing message content]';
    }
  }
}

// Interface cho kết quả trả về
export interface Base64Result {
  base64String: string;
  mimeType: string;
}
