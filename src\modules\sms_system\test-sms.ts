import { NestFactory } from '@nestjs/core';
import { AppModule } from '../../app.module';
import { SmsSystemService } from './sms-system.service';
import { SmsTypeEnum } from './constants';

/**
 * Script test để kiểm tra SMS system
 */
async function testSmsSystem() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const smsSystemService = app.get(SmsSystemService);

  try {
    console.log('🚀 Bắt đầu test SMS system...');

    // Test gửi SMS OTP
    const jobId = await smsSystemService.sendOtpSms(
      '0912345678', // Số điện thoại test
      '123456',     // Mã OTP
      1,            // User ID
      {
        USER_NAME: 'Nguyễn Văn A',
        COMPANY_NAME: 'RedAI',
      }
    );

    console.log(`✅ Đã tạo job SMS OTP với ID: ${jobId}`);

    // Test gửi SMS đăng ký
    const registrationJobId = await smsSystemService.sendRegistrationSms(
      '0987654321',
      'Trần Thị B',
      2,
      {
        WELCOME_BONUS: '100.000 VND',
      }
    );

    console.log(`✅ Đã tạo job SMS đăng ký với ID: ${registrationJobId}`);

    console.log('🎉 Test SMS system hoàn thành!');
  } catch (error) {
    console.error('❌ Lỗi khi test SMS system:', error.message);
  } finally {
    await app.close();
  }
}

// Chạy test nếu file được gọi trực tiếp
if (require.main === module) {
  testSmsSystem();
}
