# App Configuration
PORT=3000
NODE_ENV=development
API_PREFIX=api

# Database
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_DATABASE=mydatabase
DB_SSL=false

# Redis
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# JWT
JWT_SECRET=your-jwt-secret
JWT_EXPIRATION_TIME=1d
JWT_REFRESH_SECRET=your-refresh-secret
JWT_REFRESH_EXPIRATION_TIME=7d

# Cloudflare R2
CF_R2_ENDPOINT=https://your-endpoint.r2.cloudflarestorage.com
CF_R2_REGION=auto
CF_R2_ACCESS_KEY=your-access-key
CF_R2_SECRET_KEY=your-secret-key
CF_BUCKET_NAME=your-bucket

# CDN
CDN_URL=https://your-cdn.domain.com
CDN_SECRET_KEY=your-cdn-secret

# OpenAI
OPENAI_API_KEY=your-api-key
OPENAI_ORGANIZATION_ID=your-org-id

# Google
GOOGLE_CLIENT_ID=your-client-id
GOOGLE_CLIENT_SECRET=your-client-secret

MAIL_HOST=
MAIL_PASSWORD=
MAIL_USERNAME=
MAIL_PORT=
MAIL_SECURE=
MAIL_DEFAULT_FROM=

# FPT SMS Configuration
FPT_SMS_CLIENT_ID=your-fpt-sms-client-id
FPT_SMS_CLIENT_SECRET=your-fpt-sms-client-secret
FPT_SMS_SCOPE=send_brandname_otp send_brandname
FPT_SMS_API_URL=http://api.fpt.net/api
FPT_SMS_BRANDNAME=your-brandname
