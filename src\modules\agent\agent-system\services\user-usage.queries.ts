import { Injectable, Logger } from '@nestjs/common';
import { ChatDatabaseService } from '../database.service';

@Injectable()
export class UserUsageQueries {
  private readonly logger = new Logger(UserUsageQueries.name);

  constructor(private readonly databaseService: ChatDatabaseService) {}

  async getPointBalanceByUserId(userId: number): Promise<number> {
    const query = `SELECT points_balance
                   from users
                   where id = $1;`;
    const values = [userId];
    try {
      const result = await this.databaseService.query(query, values);
      if (!result?.length) {
        this.logger.error(`No user found with id = ${userId}`);
        throw new Error('No user found');
      }
      return result[0].point_balance;
    } catch (error) {
      this.logger.error(error?.stack | error.message);
      throw error;
    }
  }

  async updatePointBalanceByUserId(
    usage: number,
    userId: number,
  ): Promise<number> {
    const query = `UPDATE users
                   SET points_balance = GREATEST(0, points_balance - $1)
                   WHERE id = $2
                   RETURNING points_balance;`;
    const values = [usage, userId];
    try {
      const result = await this.databaseService.query(query, values);
      this.logger.debug(JSON.stringify(result, null, 2));
      if (!result?.length) {
        this.logger.error(`No user found with id = ${userId}`);
        throw new Error('No user found');
      }
      return result[0][0].points_balance;
    } catch (error) {
      this.logger.error(error?.stack | error.message);
      throw error;
    }
  }
}
