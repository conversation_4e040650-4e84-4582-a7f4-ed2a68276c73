// src/redis/redis.service.ts
import { Injectable, OnModuleDestroy } from '@nestjs/common';
import Redis from 'ioredis';
import { env } from '../../config';

@Injectable()
export class RedisService implements OnModuleDestroy {
  private readonly redis: Redis;

  constructor() {
    const url = env.external.REDIS_URL;
    this.redis = new Redis(url);
  }

  async xadd(
    stream: string,
    data: Record<string, string>,
  ): Promise<string | null> {
    return this.redis.xadd(stream, '*', ...Object.entries(data).flat());
  }

  async xread(stream: string, lastId: string = '$'): Promise<any> {
    return this.redis.xread('BLOCK', 0, 'STREAMS', stream, lastId);
  }

  getRawClient() {
    return this.redis;
  }

  onModuleDestroy() {
    this.redis.disconnect();
  }
}
