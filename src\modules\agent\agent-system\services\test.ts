// import { StateGraph, START, END, Annotation } from "@langchain/langgraph";
// import { MemorySaver } from "@langchain/langgraph";
//
// const GraphState = Annotation.Root({
//   input: Annotation<string[], string[]>({
//     reducer: (x, y) => [...x, ...y],
//     default: () => [],
//   })
// });
//
// const step1 = (state: typeof GraphState.State) => {
//   console.log("---Step 1---");
//   return {
//     input: ['step1'],
//   };
// }
//
// const step2 = (state: typeof GraphState.State) => {
//   console.log("---Step 2---");
//   return {
//     input: ['step2'],
//   };
// }
//
// const step3 = (state: typeof GraphState.State) => {
//   console.log("---Step 3---");
//   return {
//     input: ['step3'],
//   };
// }
//
//
// const builder = new StateGraph(GraphState)
//   .addNode("step1", step1)
//   .addNode("step2", step2)
//   .addNode("step3", step3)
//   .addEdge(START, "step1")
//   .addEdge("step1", "step2")
//   .addEdge("step2", "step3")
//   .addEdge("step3", END);
//
//
// // Set up memory
// const graphStateMemory = new MemorySaver()
//
// const graph = builder.compile({
//   checkpointer: graphStateMemory,
// });
//
//
//
// // Input
// const initialInput = { input: ["hello world"] };
//
// // Thread
// const graphStateConfig = { configurable: { thread_id: "1" }, streamMode: "values" as const };
//
// async function main() {
//
// // Run the graph until the first interruption
//   for await (const event of await graph.stream(initialInput, graphStateConfig)) {
//     console.log(`--- ${event.input} ---`);
//   }
//
// // Will log when the graph is interrupted, after step 2.
//   console.log("--- GRAPH INTERRUPTED ---");
//
//   console.log("Current state!")
//   const currState = await graph.getState(graphStateConfig);
//   console.log(currState.values)
//
//   await graph.updateState(graphStateConfig, { input: ["hello universe!"] })
//
//   console.log("---\n---\nUpdated state!")
//   const updatedState = await graph.getState(graphStateConfig);
//   console.log(updatedState.values)
// }
//
// main().catch(console.error);