# SMS System Module

Module xử lý hệ thống SMS cho RedAI Worker, hỗ trợ gửi SMS thông qua FPT SMS service với template động.

## Tính năng

- ✅ Xử lý job SMS từ queue
- ✅ Quản lý template SMS từ database
- ✅ Thay thế biến động trong template (cú pháp `{{BIEN}}`)
- ✅ Tích hợp FPT SMS Brandname service
- ✅ Hỗ trợ nhiều loại SMS (OTP, đăng ký, thông báo, etc.)
- ✅ Logging và error handling

## Cấu trúc

```
src/modules/sms_system/
├── constants/
│   ├── sms-type.enum.ts        # Enum các loại SMS
│   ├── sms-job-name.enum.ts    # Enum tên job
│   └── index.ts
├── dto/
│   └── sms-system-job.dto.ts   # Interface job data
├── entities/
│   └── admin-template-sms.entity.ts  # Entity template SMS
├── services/
│   └── sms-template.service.ts # Service xử lý template
├── scripts/
│   └── seed-sms-templates.ts   # Script seed template mẫu
├── sms-system.processor.ts     # Job processor
├── sms-system.service.ts       # Service chính
├── sms-system.module.ts        # Module definition
├── test-sms.ts                 # Script test
├── index.ts                    # Export tất cả
└── README.md
```

## Cấu hình

### Biến môi trường

```env
# FPT SMS Configuration
FPT_SMS_CLIENT_ID=your_client_id
FPT_SMS_CLIENT_SECRET=your_client_secret
FPT_SMS_SCOPE=send_brandname_otp send_brandname
FPT_SMS_API_URL=http://api.fpt.net/api
FPT_SMS_BRANDNAME=REDAI
```

### Database

Module sử dụng bảng `admin_template_sms`:

```sql
CREATE TABLE admin_template_sms (
    id SERIAL PRIMARY KEY,
    category VARCHAR(100) UNIQUE,
    content TEXT,
    created_at BIGINT,
    updated_at BIGINT,
    placeholders JSON,
    name VARCHAR(100)
);
```

## Sử dụng

### 1. Seed template SMS

```bash
# Chạy script seed để tạo template mẫu
npx ts-node src/modules/sms_system/scripts/seed-sms-templates.ts
```

### 2. Gửi SMS từ ứng dụng chính

```typescript
import { SmsSystemService, SmsTypeEnum } from '@/modules/sms_system';

// Inject service
constructor(private readonly smsSystemService: SmsSystemService) {}

// Gửi SMS OTP
await this.smsSystemService.sendOtpSms(
  '0912345678',  // Số điện thoại
  '123456',      // Mã OTP
  1,             // User ID (optional)
  {              // Dữ liệu bổ sung
    USER_NAME: 'Nguyễn Văn A',
    COMPANY_NAME: 'RedAI',
  }
);

// Gửi SMS đăng ký
await this.smsSystemService.sendRegistrationSms(
  '0987654321',
  'Trần Thị B',
  2,
  { WELCOME_BONUS: '100.000 VND' }
);

// Gửi SMS tùy chỉnh
await this.smsSystemService.sendSmsSystemJob({
  phone: '0912345678',
  message: '', // Sẽ được thay thế bởi template
  type: SmsTypeEnum.FORGOT_PASSWORD,
  data: {
    RESET_CODE: 'ABC123',
    USER_NAME: 'Nguyễn Văn A',
  },
});
```

### 3. Template SMS

Template SMS sử dụng cú pháp `{{BIEN}}` để thay thế biến:

```
Ma xac thuc cua ban la: {{OTP_CODE}}. Vui long khong chia se ma nay voi ai khac.
```

Sẽ được thay thế thành:

```
Ma xac thuc cua ban la: 123456. Vui long khong chia se ma nay voi ai khac.
```

### 4. Các loại SMS hỗ trợ

- `OTP`: SMS xác thực OTP
- `REGISTRATION`: SMS thông báo đăng ký
- `LOGIN`: SMS thông báo đăng nhập
- `FORGOT_PASSWORD`: SMS quên mật khẩu
- `CHANGE_PASSWORD`: SMS thay đổi mật khẩu
- `EMAIL_VERIFICATION`: SMS xác thực email
- `PHONE_VERIFICATION`: SMS xác thực số điện thoại
- `TRANSACTION`: SMS thông báo giao dịch
- `PROMOTION`: SMS khuyến mãi
- `SYSTEM_NOTIFICATION`: SMS thông báo hệ thống
- `SECURITY_ALERT`: SMS cảnh báo bảo mật
- `ACCOUNT_NOTIFICATION`: SMS thông báo tài khoản

## Testing

```bash
# Test SMS system
npx ts-node src/modules/sms_system/test-sms.ts

# Kiểm tra queue dashboard
# Truy cập: http://localhost:3000/queues
# User: admin / Pass: redai@123
```

## Luồng xử lý

1. **App chính** đẩy job vào queue SMS
2. **SmsSystemProcessor** nhận job từ queue
3. **SmsTemplateService** lấy template từ database theo type
4. **SmsTemplateService** thay thế biến trong template
5. **FprSmsBrandnameService** gửi SMS qua FPT API
6. **Logging** kết quả gửi SMS

## Lưu ý

- Template SMS phải tồn tại trong database trước khi gửi
- Biến trong template phải match với data được truyền vào
- FPT SMS có giới hạn về độ dài tin nhắn và ký tự đặc biệt
- Job sẽ retry 3 lần nếu gặp lỗi
- Sử dụng brandname "REDAI" theo cấu hình môi trường
