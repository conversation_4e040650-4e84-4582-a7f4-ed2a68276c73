import { Injectable, OnModuleInit, OnM<PERSON>uleDestroy } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { InjectDataSource } from '@nestjs/typeorm';

/**
 * Database service for raw SQL operations (Agent Module)
 * 
 * This service provides access to TypeORM DataSource for executing raw SQL queries
 * without using entities or repositories. The agent module primarily needs to query
 * user_agent_runs table by ID and update run status.
 */
@Injectable()
export class ChatDatabaseService implements OnModuleInit, OnModuleDestroy {
  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
  ) {}

  async onModuleInit() {
    // Ensure database connection is established
    if (!this.dataSource.isInitialized) {
      await this.dataSource.initialize();
    }
  }

  async onModuleDestroy() {
    // Clean up database connection
    if (this.dataSource.isInitialized) {
      await this.dataSource.destroy();
    }
  }

  /**
   * Get the TypeORM DataSource instance for raw SQL operations
   * @returns DataSource instance
   */
  getDataSource(): DataSource {
    return this.dataSource;
  }

  /**
   * Execute a raw SQL query
   * @param query SQL query string
   * @param parameters Query parameters
   * @returns Promise<any[]> Query results
   */
  async query(query: string, parameters?: any[]): Promise<any[]> {
    try {
      return await this.dataSource.query(query, parameters);
    } catch (error) {
      throw new Error(`Database query failed: ${error.message}`);
    }
  }

  /**
   * Execute a raw SQL query and return a single result
   * @param query SQL query string
   * @param parameters Query parameters
   * @returns Promise<any | null> Single result or null
   */
  async queryOne(query: string, parameters?: any[]): Promise<any | null> {
    const results = await this.query(query, parameters);
    return results.length > 0 ? results[0] : null;
  }
}
