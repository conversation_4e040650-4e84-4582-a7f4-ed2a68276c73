import { Injectable, Logger } from '@nestjs/common';
import { ThreadConfigurationService as IThreadConfigurationService } from '../interfaces/service.interface';
import { CustomConfigurableType, ThreadConfiguration } from '../schemas';
import { apiKeyEncryption } from '../../helpers/api-key-encryption.helper';
import { ValidationService } from './validation.service';
import { workflow } from '../core';

/**
 * Service responsible for building thread configuration and handling API key decryption
 * Extracted from AgentSystemService.processAgentThread method (Section 1: Configuration & Setup)
 */
@Injectable()
export class ThreadConfigurationService implements IThreadConfigurationService {
  private readonly logger = new Logger(ThreadConfigurationService.name);

  constructor(private readonly validationService: ValidationService) {}

  /**
   * Build complete thread configuration from run data
   * @param runData - Run data from database containing payload
   * @param threadId - Thread ID for the configuration
   * @param jwt - JWT token for authentication
   * @returns Complete thread configuration
   */
  async buildConfiguration(
    runData: any,
    threadId: string,
    jwt: string,
  ): Promise<ThreadConfiguration> {
    try {
      this.logger.debug(
        `Building thread configuration for thread ${threadId}`,
        {
          threadId,
          runId: runData.id,
          userId: runData.created_by,
          hasJwt: !!jwt,
          payloadSize: JSON.stringify(runData.payload).length,
        },
      );

      // Extract user ID from run data
      const userId = runData.created_by as number;

      const latestState = await workflow.getState({
        configurable: {
          thread_id: threadId,
        },
      });

      const checkpointId = latestState?.config?.configurable?.checkpoint_id || undefined;

      // Build custom configuration for LangGraph
      const customConfig = this.buildCustomConfigurable(runData, threadId, checkpointId);

      // Decrypt payload for message extraction
      const decryptedPayload = this.decryptApiKeysInPayload(
        runData.payload,
        userId,
      );

      // Validate custom configuration using Zod
      const customConfigValidation =
        this.validationService.validateCustomConfigurable(customConfig);
      if (!customConfigValidation.success) {
        const errorMessage = this.validationService.formatValidationErrors(
          customConfigValidation.issues || [],
        );
        this.logger.error(
          `Custom configuration validation failed for thread ${threadId}:`,
          {
            threadId,
            errors: customConfigValidation.issues,
          },
        );
        throw new Error(`Invalid custom configuration: ${errorMessage}`);
      }

      // Validate agent config map if present
      if (customConfig.agentConfigMap) {
        const agentConfigMapValidation =
          this.validationService.validateSystemAgentConfigMap(
            customConfig.agentConfigMap,
          );
        if (!agentConfigMapValidation.success) {
          const errorMessage = this.validationService.formatValidationErrors(
            agentConfigMapValidation.issues || [],
          );
          this.logger.error(
            `Agent configuration map validation failed for thread ${threadId}:`,
            {
              threadId,
              errors: agentConfigMapValidation.issues,
            },
          );
          throw new Error(`Invalid agent configuration map: ${errorMessage}`);
        }

        this.logger.debug(
          `✅ Agent configuration map validation passed for thread ${threadId}`,
          {
            threadId,
            agentCount: Object.keys(customConfig.agentConfigMap).length,
          },
        );
      }

      // ✅ NEW: Extract message data from payload
      const messageData = this.extractMessageData(decryptedPayload);

      const configuration: ThreadConfiguration = {
        customConfig,
        decryptedPayload,
        userId,
        threadId,
        runData,
        jwt,
        messageData, // ✅ NEW: Include extracted message data
      };

      // Validate the final thread configuration using Zod
      const configValidation =
        this.validationService.validateThreadConfiguration(configuration);
      if (!configValidation.success) {
        const errorMessage = this.validationService.formatValidationErrors(
          configValidation.issues || [],
        );
        throw new Error(`Invalid thread configuration: ${errorMessage}`);
      }

      this.logger.debug(
        `Successfully built thread configuration for thread ${threadId}`,
        {
          threadId,
          userId,
          agentConfigCount: customConfig.agentConfigMap
            ? Object.keys(customConfig.agentConfigMap).length
            : 0,
          supervisorAgentId: customConfig.supervisorAgentId,
          alwaysApproveToolCall: customConfig.alwaysApproveToolCall,
        },
      );

      return configuration;
    } catch (error) {
      this.logger.error(
        `Failed to build thread configuration for thread ${threadId}:`,
        {
          threadId,
          runId: runData.id,
          error: error.message,
          stack: error.stack,
        },
      );
      throw error;
    }
  }

  /**
   * Map payload data to CustomConfigurableType for LangGraph configuration
   * @param runData Run data from database containing payload
   * @param threadId Thread ID for the configuration
   * @param checkpointId
   * @returns CustomConfigurableType configuration object
   */
  private buildCustomConfigurable(
    runData: any,
    threadId: string,
    checkpointId?: string,
  ): CustomConfigurableType {
    try {
      // Decrypt API keys in payload before processing
      const userId = runData.created_by;
      const decryptedPayload = this.decryptApiKeysInPayload(
        runData.payload,
        userId,
      );

      // Extract configuration values from decrypted payload with fallbacks
      const alwaysApproveToolCall =
        decryptedPayload?.processing?.alwaysApproveToolCall || false;
      const agentConfigMap = decryptedPayload?.agentConfigMap || {};
      const supervisorAgentId = decryptedPayload?.primaryAgentId || '';

      const config: CustomConfigurableType = {
        alwaysApproveToolCall,
        thread_id: threadId,
        checkpoint_id: checkpointId,
        agentConfigMap,
        supervisorAgentId,
        multiMcpClients: undefined, // Skip for now as requested
      };

      this.logger.debug(
        `Built CustomConfigurableType for thread ${threadId}:`,
        {
          alwaysApproveToolCall,
          thread_id: threadId,
          supervisorAgentId,
          agentConfigCount: Object.keys(agentConfigMap).length,
          hasMultiMcp: false,
          userId: userId,
          apiKeysDecrypted: true,
        },
      );

      return config;
    } catch (error) {
      this.logger.error(
        `Failed to build CustomConfigurableType for thread ${threadId}:`,
        {
          threadId,
          error: error.message,
          stack: error.stack,
        },
      );

      // Return minimal fallback configuration
      return {
        alwaysApproveToolCall: false,
        thread_id: threadId,
        agentConfigMap: {},
        supervisorAgentId: '',
        multiMcpClients: undefined,
      };
    }
  }

  /**
   * Extract message data from decrypted payload
   * @param decryptedPayload Decrypted payload containing message data
   * @returns Extracted message data object
   */
  private extractMessageData(decryptedPayload: any): any {
    try {
      const messageRequest = decryptedPayload?.message || {};

      const messageData = {
        contentBlocks: messageRequest.contentBlocks || [],
        attachmentContext: messageRequest.attachmentContext || [],
        replyToMessageId: messageRequest.replyToMessageId,
        alwaysApproveToolCall: messageRequest.alwaysApproveToolCall || false,
      };

      this.logger.debug(`Extracted message data:`, {
        contentBlockCount: messageData.contentBlocks.length,
        attachmentCount: messageData.attachmentContext.length,
        // ✅ REMOVED: threadId logging - threadId is available from context, not from messageData
        alwaysApproveToolCall: messageData.alwaysApproveToolCall,
        contentBlockTypes: messageData.contentBlocks.map(
          (block: any) => block.type,
        ),
      });

      return messageData;
    } catch (error) {
      this.logger.error(`Failed to extract message data:`, {
        error: error.message,
        stack: error.stack,
      });

      // Return empty message data as fallback
      return {
        contentBlocks: [],
        attachmentContext: [],
        threadId: '',
        alwaysApproveToolCall: false,
      };
    }
  }

  /**
   * Decrypt API keys in the payload based on user type
   * @param payload The payload containing encrypted API keys
   * @param userId User ID for decryption (from created_by field)
   * @returns Payload with decrypted API keys
   */
  private decryptApiKeysInPayload(payload: any, userId: number): any {
    try {
      if (!payload) {
        return payload;
      }

      // Deep clone payload to avoid modifying original
      const decryptedPayload = JSON.parse(JSON.stringify(payload));

      // Decrypt API keys in agentConfigMap
      if (decryptedPayload.agentConfigMap) {
        for (const agentId in decryptedPayload.agentConfigMap) {
          const agentConfig = decryptedPayload.agentConfigMap[agentId];

          if (
            agentConfig?.model?.apiKeys &&
            Array.isArray(agentConfig.model.apiKeys)
          ) {
            this.logger.debug(
              `Decrypting ${agentConfig.model.apiKeys.length} API keys for agent ${agentId}`,
              { agentId, userId, keyCount: agentConfig.model.apiKeys.length },
            );

            agentConfig.model.apiKeys = agentConfig.model.apiKeys.map(
              (encryptedKey: string) => {
                try {
                  // Determine if this is admin or user key based on agent type
                  if (agentConfig.model.type === 'SYSTEM') {
                    // System agents use admin keys
                    return apiKeyEncryption.decryptAdminApiKey(encryptedKey);
                  } else {
                    // User agents use user-specific keys
                    return apiKeyEncryption.decryptUserApiKey(
                      encryptedKey,
                      userId,
                    );
                  }
                } catch (error) {
                  this.logger.error(
                    `Failed to decrypt API key for agent ${agentId}:`,
                    {
                      agentId,
                      userId,
                      error: error.message,
                    },
                  );
                  // Return original key if decryption fails (might already be decrypted)
                  return encryptedKey;
                }
              },
            );
          }
        }
      }

      this.logger.debug(
        `Successfully decrypted API keys in payload for user ${userId}`,
        {
          userId,
          hasAgentConfigMap: !!decryptedPayload.agentConfigMap,
          agentCount: decryptedPayload.agentConfigMap
            ? Object.keys(decryptedPayload.agentConfigMap).length
            : 0,
        },
      );

      return decryptedPayload;
    } catch (error) {
      this.logger.error(
        `Failed to decrypt API keys in payload for user ${userId}:`,
        {
          userId,
          error: error.message,
          stack: error.stack,
        },
      );
      // Return original payload if decryption fails
      return payload;
    }
  }
}
