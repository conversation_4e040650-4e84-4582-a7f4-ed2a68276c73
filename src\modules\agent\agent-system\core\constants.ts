const DEFAULT_TRIMMING_STRATEGY = 'top_k';
const DEFAULT_TRIMMING_THRESHOLD = 20;
const SUPERVISOR_TOOL_CALL_TAG = 'supervisor_tool_call';
const WORKER_TOOL_CALL_TAG = 'worker_tool_call';
const SUPERVISOR_TAG = 'supervisor';
const WORKER_TAG = 'worker';

// Role tag arrays for event transformation
const ROLE_TAGS = [SUPERVISOR_TAG, WORKER_TAG];
const TOOL_CALL_TAGS = [SUPERVISOR_TOOL_CALL_TAG, WORKER_TOOL_CALL_TAG];

export {
  DEFAULT_TRIMMING_STRATEGY,
  DEFAULT_TRIMMING_THRESHOLD,
  SUPERVISOR_TOOL_CALL_TAG,
  WORKER_TOOL_CALL_TAG,
  SUPERVISOR_TAG,
  WORKER_TAG,
  ROLE_TAGS,
  TOOL_CALL_TAGS,
};
