import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng user_audience trong cơ sở dữ liệu
 * Bảng khách hàng của người dùng
 */
@Entity('user_audience')
export class UserAudience {
  /**
   * ID của audience
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID của người dùng
   */
  @Column({ name: 'user_id', nullable: true, comment: 'Mã khách hàng' })
  userId: number;

  // Không sử dụng quan hệ với bảng User, chỉ lưu ID

  /**
   * Email của khách hàng
   */
  @Column({
    name: 'email',
    length: 255,
    nullable: true,
    comment: 'Email người dùng',
  })
  email: string;

  /**
   * Số điện thoại của khách hàng
   */
  @Column({
    name: 'phone',
    length: 20,
    nullable: true,
    comment: '<PERSON><PERSON> điện thoại',
  })
  phone: string;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', comment: 'Ngày tạo' })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    nullable: true,
    comment: 'Ngày cập nhật',
  })
  updatedAt: number;

  // Không sử dụng quan hệ với các bảng khác, chỉ lưu ID
}
