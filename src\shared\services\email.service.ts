import { Injectable, Logger } from '@nestjs/common';
import * as nodemailer from 'nodemailer';
import { env } from '../../config/env';

/**
 * Service xử lý gửi email
 */
@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private transporter: nodemailer.Transporter;

  constructor() {
    // Cấu hình transporter cho nodemailer
    this.transporter = nodemailer.createTransport({
      host: env.email.MAIL_HOST,
      port: Number(env.email.MAIL_PORT),
      secure: env.email.MAIL_SECURE,
      auth: {
        user: env.email.MAIL_USERNAME,
        pass: env.email.MAIL_PASSWORD,
      },
    });
  }

  /**
   * Gửi email
   * @param to Địa chỉ email người nhận
   * @param subject Tiêu đề email
   * @param html Nội dung email dạng HTML
   * @param from Địa chỉ email người gửi (mặc định lấy từ biến môi trường)
   * @returns Kết quả gửi email
   */
  async sendEmail(
    to: string | string[],
    subject: string,
    html: string,
    from: string = env.email.MAIL_DEFAULT_FROM || '<EMAIL>',
  ): Promise<boolean> {
    try {
      const mailOptions = {
        from,
        to,
        subject,
        html,
      };

      const info = await this.transporter.sendMail(mailOptions);
      this.logger.log(`Email sent: ${info.messageId}`);
      return true;
    } catch (error) {
      this.logger.error('Failed to send email', error);
      return false;
    }
  }
}
