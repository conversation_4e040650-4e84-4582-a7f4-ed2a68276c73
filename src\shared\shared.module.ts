import { Global, Module } from '@nestjs/common';
import { EmailService } from './services/email.service';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { env } from '../config';

/**
 * Module chứa các service dùng chung trong toàn bộ ứng dụng
 */
@Global()
@Module({
  imports: [
    ClientsModule.register([
      {
        name: 'REDIS_SUBSCRIBER',
        transport: Transport.REDIS,
        options: {
          // Parse Redis URL to extract connection details
          ...parseRedisUrl(env.external.REDIS_URL),
          retryDelay: 1000,
          maxRetriesPerRequest: 3,
        },
      },
    ]),
  ],
  providers: [EmailService],
  exports: [EmailService, ClientsModule],
})
export class SharedModule {}


/**
 * Parse Redis URL to extract connection options
 * @param redisUrl Redis URL string
 * @returns Redis connection options
 */
function parseRedisUrl(redisUrl: string) {
  try {
    const url = new URL(redisUrl);

    return {
      host: url.hostname,
      port: parseInt(url.port) || 6379,
      password: url.password || undefined,
      db: parseInt(url.pathname.slice(1)) || 0, // Extract DB from URL path
    };
  } catch (error) {
    // Fallback to default values if URL parsing fails
    return {
      host: 'localhost',
      port: 6379,
      db: 0,
    };
  }
}