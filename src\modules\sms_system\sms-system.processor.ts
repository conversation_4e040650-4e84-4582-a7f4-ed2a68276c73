import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName } from '../../queue';
import { SmsSystemJobData } from './dto/sms-system-job.dto';
import { SmsJobName } from './constants';
import { SmsTemplateService } from './services/sms-template.service';
import { SmsEncryptionService } from './services/sms-encryption.service';
import { FprSmsBrandnameService } from '../../shared/services/sms/fpr-sms-brandname.service';

/**
 * Processor xử lý queue gửi SMS hệ thống
 */
@Injectable()
@Processor(QueueName.SMS)
export class SmsSystemProcessor extends WorkerHost {
  private readonly logger = new Logger(SmsSystemProcessor.name);

  constructor(
    private readonly smsTemplateService: SmsTemplateService,
    private readonly smsEncryptionService: SmsEncryptionService,
    private readonly fprSmsBrandnameService: FprSmsBrandnameService,
  ) {
    super();
  }

  /**
   * <PERSON><PERSON> lý job gửi SMS hệ thống
   * @param job Job chứa dữ liệu SMS
   */
  async process(job: Job<SmsSystemJobData, any, string>): Promise<void> {
    this.logger.log(`Bắt đầu xử lý job SMS: ${job.id} - Type: ${job.data.type}`);

    try {
      switch (job.name) {
        case SmsJobName.SMS_SYSTEM:
          await this.processSmsSystemJob(job);
          break;
        default:
          this.logger.warn(`Job name không được hỗ trợ: ${job.name}`);
          throw new Error(`Job name không được hỗ trợ: ${job.name}`);
      }

      this.logger.log(`Đã xử lý thành công job SMS: ${job.id}`);
    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý job SMS: ${job.id} - ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xử lý job gửi SMS hệ thống
   * @param job Job chứa dữ liệu SMS hệ thống
   */
  private async processSmsSystemJob(job: Job<SmsSystemJobData>): Promise<void> {
    const { phone, type, data, userId } = job.data;

    this.logger.log(
      `Xử lý SMS hệ thống - Phone: ${phone}, Type: ${type}${
        userId ? `, UserId: ${userId}` : ''
      }`,
    );

    try {
      // Bước 1: Lấy template SMS từ database dựa vào type
      const processedContent = await this.smsTemplateService.processTemplate(type, data);

      // Bước 2: Mã hóa nội dung SMS OTP
      const encryptedContent = this.smsEncryptionService.encryptOtpContent(processedContent, userId);

      this.logger.log(`Đã mã hóa nội dung SMS cho phone: ${phone}`);

      // Bước 3: Gửi SMS qua FPT SMS service với nội dung đã mã hóa
      const result = await this.fprSmsBrandnameService.sendOtp({
        BrandName: process.env.FPT_SMS_BRANDNAME || 'REDAI',
        Phone: phone,
        Message: encryptedContent,
      });

      if (result.Status === 'Success') {
        this.logger.log(
          `Đã gửi SMS OTP mã hóa thành công - Phone: ${phone}, MessageId: ${result.MessageId}`,
        );
      } else {
        this.logger.error(
          `Gửi SMS OTP mã hóa thất bại - Phone: ${phone}, Error: ${result.ErrorMessage}`,
        );
        throw new Error(`Gửi SMS OTP mã hóa thất bại: ${result.ErrorMessage}`);
      }
    } catch (error) {
      // Phân loại lỗi để xử lý phù hợp
      let errorMessage = error.message;
      if (error.code === 'ETIMEDOUT') {
        errorMessage = `Timeout khi kết nối đến FPT SMS API: ${error.message}`;
      } else if (error.code === 'ECONNREFUSED') {
        errorMessage = `Không thể kết nối đến FPT SMS API: ${error.message}`;
      } else if (error.code === 'ENOTFOUND') {
        errorMessage = `Không tìm thấy FPT SMS API server: ${error.message}`;
      }

      this.logger.error(
        `Lỗi khi xử lý SMS hệ thống - Phone: ${phone}, Type: ${type}, Error: ${errorMessage}`,
        error.stack,
      );

      // Có thể thêm logic retry hoặc fallback ở đây
      throw new Error(errorMessage);
    }
  }
}
