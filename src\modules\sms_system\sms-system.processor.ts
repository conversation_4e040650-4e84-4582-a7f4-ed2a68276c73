import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName } from '../../queue';
import { SmsSystemJobData } from './dto/sms-system-job.dto';
import { SmsJobName } from './constants';
import { SmsTemplateService } from './services/sms-template.service';
import { FprSmsBrandnameService } from '../../shared/services/sms/fpr-sms-brandname.service';

/**
 * Processor xử lý queue gửi SMS hệ thống
 */
@Injectable()
@Processor(QueueName.SMS)
export class SmsSystemProcessor extends WorkerHost {
  private readonly logger = new Logger(SmsSystemProcessor.name);

  constructor(
    private readonly smsTemplateService: SmsTemplateService,
    private readonly fprSmsBrandnameService: FprSmsBrandnameService,
  ) {
    super();
  }

  /**
   * <PERSON><PERSON> lý job gửi SMS hệ thống
   * @param job Job chứa dữ liệu SMS
   */
  async process(job: Job<SmsSystemJobData, any, string>): Promise<void> {
    this.logger.log(`Bắt đầu xử lý job SMS: ${job.id} - Type: ${job.data.type}`);

    try {
      switch (job.name) {
        case SmsJobName.SMS_SYSTEM:
          await this.processSmsSystemJob(job);
          break;
        default:
          this.logger.warn(`Job name không được hỗ trợ: ${job.name}`);
          throw new Error(`Job name không được hỗ trợ: ${job.name}`);
      }

      this.logger.log(`Đã xử lý thành công job SMS: ${job.id}`);
    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý job SMS: ${job.id} - ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xử lý job gửi SMS hệ thống
   * @param job Job chứa dữ liệu SMS hệ thống
   */
  private async processSmsSystemJob(job: Job<SmsSystemJobData>): Promise<void> {
    const { phone, type, data, userId } = job.data;

    this.logger.log(
      `Xử lý SMS hệ thống - Phone: ${phone}, Type: ${type}${
        userId ? `, UserId: ${userId}` : ''
      }`,
    );

    try {
      // Bước 1: Lấy template SMS từ database dựa vào type
      const processedContent = await this.smsTemplateService.processTemplate(type, data);

      // Bước 2: Gửi SMS qua FPT SMS service
      const result = await this.fprSmsBrandnameService.sendOtp({
        BrandName: process.env.FPT_SMS_BRANDNAME || 'REDAI',
        Phone: phone,
        Message: processedContent,
      });

      if (result.Status === 'Success') {
        this.logger.log(
          `Đã gửi SMS thành công - Phone: ${phone}, MessageId: ${result.MessageId}`,
        );
      } else {
        this.logger.error(
          `Gửi SMS thất bại - Phone: ${phone}, Error: ${result.ErrorMessage}`,
        );
        throw new Error(`Gửi SMS thất bại: ${result.ErrorMessage}`);
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý SMS hệ thống - Phone: ${phone}, Type: ${type}, Error: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
