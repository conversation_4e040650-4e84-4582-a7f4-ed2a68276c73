/**
 * Script test mã hóa SMS Base64
 */

// Test mã hóa Base64 đơn giản
function testBase64Encryption() {
  console.log('=== Test Mã Hóa SMS Base64 ===\n');

  // Test cases
  const testCases = [
    'Mã xác thực của bạn là: 123456',
    'Your OTP code is: 789012',
    'Mã OTP RedAI: 456789. Không chia sẻ mã này với ai!',
    'Chào mừng bạn đến với RedAI! Mã xác thực: 111222'
  ];

  testCases.forEach((originalContent, index) => {
    console.log(`--- Test Case ${index + 1} ---`);
    console.log(`Nội dung gốc: ${originalContent}`);
    
    // Mã hóa Base64
    const encoded = Buffer.from(originalContent, 'utf8').toString('base64');
    console.log(`Mã hóa Base64: ${encoded}`);
    
    // G<PERSON><PERSON>i mã Base64
    const decoded = Buffer.from(encoded, 'base64').toString('utf8');
    console.log(`Giải mã Base64: ${decoded}`);
    
    // Kiểm tra tính chính xác
    const isCorrect = originalContent === decoded;
    console.log(`Kết quả: ${isCorrect ? '✅ ĐÚNG' : '❌ SAI'}`);
    console.log('');
  });

  // Test với ký tự đặc biệt
  console.log('--- Test Ký Tự Đặc Biệt ---');
  const specialContent = 'Mã OTP: 123456 🔐 Hết hạn sau 5 phút ⏰';
  console.log(`Nội dung gốc: ${specialContent}`);
  
  const specialEncoded = Buffer.from(specialContent, 'utf8').toString('base64');
  console.log(`Mã hóa Base64: ${specialEncoded}`);
  
  const specialDecoded = Buffer.from(specialEncoded, 'base64').toString('utf8');
  console.log(`Giải mã Base64: ${specialDecoded}`);
  
  const specialIsCorrect = specialContent === specialDecoded;
  console.log(`Kết quả: ${specialIsCorrect ? '✅ ĐÚNG' : '❌ SAI'}`);
  console.log('');

  // Test kiểm tra Base64 hợp lệ
  console.log('--- Test Kiểm Tra Base64 Hợp Lệ ---');
  const validBase64 = Buffer.from('Hello World', 'utf8').toString('base64');
  const invalidBase64 = 'This is not base64!';
  
  console.log(`Valid Base64: ${validBase64}`);
  console.log(`Invalid text: ${invalidBase64}`);
  
  function isBase64(str) {
    try {
      const decoded = Buffer.from(str, 'base64').toString('utf8');
      const reencoded = Buffer.from(decoded, 'utf8').toString('base64');
      return reencoded === str;
    } catch {
      return false;
    }
  }
  
  console.log(`Valid Base64 check: ${isBase64(validBase64) ? '✅ ĐÚNG' : '❌ SAI'}`);
  console.log(`Invalid text check: ${isBase64(invalidBase64) ? '❌ SAI' : '✅ ĐÚNG'}`);
  
  console.log('\n=== Test Hoàn Thành ===');
}

// Chạy test
testBase64Encryption();
