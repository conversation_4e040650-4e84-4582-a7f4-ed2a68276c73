import {
  InputModality,
  ModelFeature,
  ModelProviderEnum,
  OutputModality,
  SamplingParameter,
} from '../../enums';

export type TrimmingType = 'top_k' | 'ai' | 'token';

/**
 * User agent model configuration with user-specific API keys
 */
export interface UserModelConfig {
  name: string;
  provider: ModelProviderEnum;
  inputModalities: InputModality[];
  outputModalities: OutputModality[];
  samplingParameters: SamplingParameter[];
  features: ModelFeature[];
  parameters?: {
    temperature?: number;
    topP?: number;
    topK?: number;
    maxTokens?: number;
    maxOutputTokens?: number;
  };
  pricing: {
    inputRate: number;
    outputRate: number;
  };
  type: 'USER';
  apiKeys: string[]; // User-specific encrypted API keys
  userKeyId?: number; // Optional reference to user's key management
}

/**
 * User agent configuration with user-specific features
 */
export interface UserAgentConfig {
  id: string;
  name: string;
  description: string;
  instruction: string;
  tools?: Record<string, any>[];
  trimmingConfig?: {
    type: TrimmingType;
    threshold: number;
  };
  model: UserModelConfig;
}

/**
 * Map of user agent configurations
 */
export interface UserAgentConfigMap {
  [agentId: string]: UserAgentConfig;
}

/**
 * Type guard to check if an agent config is a user agent
 */
export function isUserAgentConfig(config: any): config is UserAgentConfig {
  return config?.model?.type === 'USER';
}

/**
 * Type guard to check if a model config is a user model
 */
export function isUserModelConfig(model: any): model is UserModelConfig {
  return model?.type === 'USER';
}

/**
 * Helper function to validate user agent configuration
 */
export function validateUserAgentConfig(config: any): config is UserAgentConfig {
  return (
    config &&
    typeof config.id === 'string' &&
    typeof config.name === 'string' &&
    typeof config.description === 'string' &&
    typeof config.instruction === 'string' &&
    config.model &&
    config.model.type === 'USER' &&
    Array.isArray(config.model.apiKeys)
  );
}

/**
 * Helper function to get user agent IDs from a config map
 */
export function getUserAgentIds(configMap: UserAgentConfigMap): string[] {
  return Object.keys(configMap);
}

/**
 * Helper function to get user agents with specific tools
 */
export function getUserAgentsWithTools(configMap: UserAgentConfigMap): UserAgentConfig[] {
  return Object.values(configMap).filter(config => config.tools && config.tools.length > 0);
}

/**
 * Helper function to get user agent by ID
 */
export function getUserAgentById(configMap: UserAgentConfigMap, agentId: string): UserAgentConfig | undefined {
  return configMap[agentId];
}

/**
 * Helper function to validate user agent config map
 */
export function validateUserAgentConfigMap(configMap: any): configMap is UserAgentConfigMap {
  if (!configMap || typeof configMap !== 'object') {
    return false;
  }
  
  return Object.values(configMap).every(config => validateUserAgentConfig(config));
}
