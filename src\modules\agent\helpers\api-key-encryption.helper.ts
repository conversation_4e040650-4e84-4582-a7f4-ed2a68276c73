import * as crypto from 'crypto';
import { env } from '../../../config';

/**
 * @file A functional module for encrypting and decrypting API keys.
 * This module provides stateless functions for handling API key security
 * for both admin and user-specific contexts.
 */

// --- Configuration and Constants ---

const ALGORITHM: string = 'aes-256-cbc';
const ENCODING: BufferEncoding = 'hex';
const TEXT_ENCODING: crypto.Encoding = 'utf8';

// Retrieve secrets from environment variables, typed to reflect they could be missing.
const ADMIN_SECRET: string | undefined = env.llmSystemEncryptionKey.ADMIN_SECRECT_MODEL;
const USER_SECRET_BASE: string | undefined = env.llmSystemEncryptionKey.USER_SECRECT_MODEL;



/**
 * The core decryption function.
 * @param {string} encryptedText - The IV-prefixed encrypted string.
 * @param {string} secret - The secret used to derive the decryption key.
 * @returns {string} The original decrypted string.
 */
const decrypt = (encryptedText: string, secret: string): string => {
  // Tạo key từ secretKey bằng cách hash với SHA-256
  const key = crypto.createHash('sha256').update(secret).digest('base64').substring(0, 32);

  // Tách IV và chuỗi đã mã hóa
  const textParts = encryptedText.split(':');
  const iv = Buffer.from(textParts[0], 'hex');
  const encryptedData = textParts[1];

  // Tạo decipher
  const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);

  // Giải mã
  let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
  decrypted += decipher.final('utf8');

  return decrypted;
};

/**
 * Decrypts an API key for an admin.
 * @param {string} encryptedApiKey The encrypted API key.
 * @returns {string} The original API key.
 * @throws {Error} If the admin secret is not configured.
 */
const decryptAdminApiKey = (encryptedApiKey: string): string => {
  if (!ADMIN_SECRET) {
    throw new Error('ADMIN_SECRECT_MODEL is not configured.');
  }
  return decrypt(encryptedApiKey, ADMIN_SECRET);
};


/**
 * Decrypts an API key for a specific user.
 * @param {string} encryptedApiKey The encrypted API key.
 * @param {number} userId The ID of the user.
 * @returns {string} The original API key.
 * @throws {Error} If the user secret is not configured.
 */
const decryptUserApiKey = (encryptedApiKey: string, userId: number): string => {
  if (!USER_SECRET_BASE) {
    throw new Error('USER_SECRECT_MODEL is not configured.');
  }
  const userSpecificSecret = `${USER_SECRET_BASE}_${userId}`;
  return decrypt(encryptedApiKey, userSpecificSecret);
};

/**
 * Exporting the public functions as a single module.
 * TypeScript infers the type of the exported object.
 */
export const apiKeyEncryption = {
  decryptAdminApiKey,
  decryptUserApiKey,
};