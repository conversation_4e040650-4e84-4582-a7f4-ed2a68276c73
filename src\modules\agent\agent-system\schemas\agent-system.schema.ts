import { z } from 'zod';
import {
  InputModality,
  ModelFeature,
  ModelProviderEnum,
  OutputModality,
  SamplingParameter,
} from '../../enums';

/**
 * Zod schema for trimming types
 */
export const TrimmingTypeSchema = z.enum(['top_k', 'ai', 'token']);

/**
 * Zod schema for MCP SSE Configuration
 */
export const McpSseConfigSchema = z.object({
  serverName: z.string().min(1, 'Server name is required'),
  config: z.record(z.any()),
});

/**
 * Zod schema for model parameters
 */
export const ModelParametersSchema = z.object({
  temperature: z.number().min(0).max(2).optional(),
  topP: z.number().min(0).max(1).optional(),
  topK: z.number().int().positive().optional(),
  maxTokens: z.number().int().positive().optional(),
  maxOutputTokens: z.number().int().positive().optional(),
}).optional();

/**
 * Zod schema for model pricing
 */
export const ModelPricingSchema = z.object({
  inputRate: z.number().nonnegative('Input rate must be non-negative'),
  outputRate: z.number().nonnegative('Output rate must be non-negative'),
});

/**
 * Zod schema for system model configuration
 */
export const SystemModelConfigSchema = z.object({
  name: z.string().min(1, 'Model name is required'),
  provider: z.nativeEnum(ModelProviderEnum),
  inputModalities: z.array(z.nativeEnum(InputModality)),
  outputModalities: z.array(z.nativeEnum(OutputModality)),
  samplingParameters: z.array(z.nativeEnum(SamplingParameter)),
  features: z.array(z.nativeEnum(ModelFeature)),
  parameters: ModelParametersSchema,
  pricing: ModelPricingSchema,
  type: z.literal('SYSTEM'),
  apiKeys: z.array(z.string().min(1, 'API key cannot be empty')).min(1, 'At least one API key is required'),
});

/**
 * Zod schema for trimming configuration
 */
export const TrimmingConfigSchema = z.object({
  type: TrimmingTypeSchema,
  threshold: z.number().int().positive('Threshold must be a positive integer'),
});

/**
 * Zod schema for system agent configuration
 */
export const SystemAgentConfigSchema = z.object({
  id: z.string().min(1, 'Agent ID is required'),
  name: z.string().min(1, 'Agent name is required'),
  description: z.string().min(1, 'Agent description is required'),
  instruction: z.string().min(1, 'Agent instruction is required'),
  mcpConfig: z.array(McpSseConfigSchema),
  vectorStoreId: z.string().nullable().optional(), // Vector store ID is optional
  isSupervisor: z.boolean(),
  trimmingConfig: TrimmingConfigSchema,
  model: SystemModelConfigSchema,
});

/**
 * Zod schema for system agent configuration map
 */
export const SystemAgentConfigMapSchema = z.record(
  z.string().min(1, 'Agent ID key cannot be empty'),
  SystemAgentConfigSchema
);

/**
 * Zod schema for custom configurable type (LangGraph configuration)
 */
export const CustomConfigurableTypeSchema = z.object({
  alwaysApproveToolCall: z.boolean().optional(),
  thread_id: z.string().optional(),
  checkpoint_id: z.string().optional(),
  agentConfigMap: SystemAgentConfigMapSchema.optional(),
  supervisorAgentId: z.string().optional(),
  multiMcpClients: z.any().optional(), // Keep as any for now
});

/**
 * Zod schema for thread configuration
 */
export const ThreadConfigurationSchema = z.object({
  customConfig: CustomConfigurableTypeSchema,
  decryptedPayload: z.any(), // Keep as any since payload structure varies
  userId: z.number().int().positive('User ID must be a positive integer'),
  threadId: z.string().min(1, 'Thread ID is required'),
  runData: z.any(), // Keep as any since run data structure varies
  jwt: z.string().min(1, 'JWT token is required'),
  // ✅ Message data with flat reply design
  messageData: z.object({
    contentBlocks: z.array(z.any()),
    attachmentContext: z.array(z.any()).optional(),
    replyToMessageId: z.string().optional(),
    alwaysApproveToolCall: z.boolean().optional(),
  }),
});

/**
 * Zod schema for streaming components
 */
export const StreamingComponentsSchema = z.object({
  producer: z.any(), // Redis client - keep as any
  tokenUsageCollector: z.any(), // TokenUsageCollector instance - keep as any
  emitEventCallback: z.function(), // Function type
  streamingInput: z.any(), // LangGraph input - keep as any
  threadConfig: z.any(), // ThreadConfiguration - keep as any for flexibility
  partialTokens: z.array(z.string()),
  streamingIterator: z.any(), // AsyncIterableIterator - keep as any
  abortListener: z.function().returns(z.promise(z.void())), // Async function
});

/**
 * Zod schema for event processing context
 */
export const EventProcessingContextSchema = z.object({
  event: z.string().min(1, 'Event type is required'),
  data: z.any(), // Event data varies by type
  tags: z.array(z.string()),
  threadId: z.string().min(1, 'Thread ID is required'),
  runData: z.any(), // Run data structure varies
  producer: z.any(), // Redis client
  partialTokens: z.array(z.string()),
  abortController: z.any(), // AbortController instance
  emitEventCallback: z.function(),
  logger: z.any(), // Logger instance
  userMessagesQueries: z.any().optional(), // Service instance
  userUsageQueries: z.any().optional(), // Service instance
});

/**
 * Zod schema for processing result
 */
export const ProcessingResultSchema = z.object({
  completed: z.boolean(),
  partialTokens: z.array(z.string()),
  error: z.instanceof(Error).optional(),
  cancelled: z.boolean().optional(),
});

/**
 * Zod schema for completion context
 */
export const CompletionContextSchema = z.object({
  threadId: z.string().min(1, 'Thread ID is required'),
  runData: z.any(), // Run data structure varies
  partialTokens: z.array(z.string()),
  abortController: z.any(), // AbortController instance
  emitEventCallback: z.any().optional(), // EmitEventCallback type - keep as any for Zod compatibility
});

/**
 * Zod schema for completion result
 */
export const CompletionResultSchema = z.object({
  success: z.boolean(),
  threadId: z.string().min(1, 'Thread ID is required'),
  runId: z.string().min(1, 'Run ID is required'),
  totalTokens: z.number().int().nonnegative().optional(),
  totalChars: z.number().int().nonnegative().optional(),
  cancelled: z.boolean().optional(),
  finalCleanup: z.boolean().optional(),
  error: z.string().optional(),
});

// Export inferred types for TypeScript
export type TrimmingType = z.infer<typeof TrimmingTypeSchema>;
export type McpSseConfig = z.infer<typeof McpSseConfigSchema>;
export type SystemModelConfig = z.infer<typeof SystemModelConfigSchema>;
export type SystemAgentConfig = z.infer<typeof SystemAgentConfigSchema>;
export type SystemAgentConfigMap = z.infer<typeof SystemAgentConfigMapSchema>;
export type CustomConfigurableType = z.infer<typeof CustomConfigurableTypeSchema>;
export type ThreadConfiguration = z.infer<typeof ThreadConfigurationSchema>;
export type StreamingComponents = z.infer<typeof StreamingComponentsSchema>;
export type EventProcessingContext = z.infer<typeof EventProcessingContextSchema>;
export type ProcessingResult = z.infer<typeof ProcessingResultSchema>;
export type CompletionContext = z.infer<typeof CompletionContextSchema>;
export type CompletionResult = z.infer<typeof CompletionResultSchema>;
