import { z, ZodError } from 'zod';
import { configDotenv } from 'dotenv';
import { Logger } from '@nestjs/common';

configDotenv();

const logger = new Logger('Config');
const requiredString = (name: string) =>
  z.string().trim().min(1, `${name} is required`);
const requiredURL = (name: string) =>
  z.string().url(`${name} must be a valid URL`);
const requiredNumber = (name: string) =>
  z
    .string()
    .transform(Number)
    .refine((v) => !isNaN(v), { message: `${name} must be a number` });

const envSchema = z.object({
  misc: z.object({
    NODE_ENV: z.enum(['development', 'test', 'staging', 'production']),
    PORT: requiredNumber('PORT'),
  }),
  app: z.object({
    BASE_URL: z.string().url().optional().default('http://localhost:3000'),
  }),
  database: z.object({
    DB_HOST: requiredString('DB_HOST'),
    DB_URL: requiredURL('DB_URL'),
    DB_PORT: requiredNumber('DB_PORT'),
    DB_USERNAME: requiredString('DB_USERNAME'),
    DB_PASSWORD: requiredString('DB_PASSWORD'),
    DB_DATABASE: requiredString('DB_DATABASE'),
    DB_SSL: z.string().transform((v) => v === 'true'),
  }),
  s3: z.object({
    S3_ACCESS_KEY: requiredString('S3_ACCESS_KEY'),
    S3_SECRET_KEY: requiredString('S3_SECRET_KEY'),
    S3_ENDPOINT: requiredURL('S3_ENDPOINT'),
    S3_BUCKET_NAME: requiredString('S3_BUCKET_NAME'),
    S3_REGION: requiredString('S3_REGION'),
  }),
  cdn: z.object({
    CDN_URL: requiredURL('CDN_URL'),
    CDN_SECRET_KEY: requiredString('CDN_SECRET_KEY'),
  }),
  openai: z.object({
    OPENAI_API_KEY: requiredString('OPENAI_API_KEY'),
  }),
  external: z.object({
    EXTERNAL_EMAIL_API_URL: requiredURL('EXTERNAL_EMAIL_API_URL'),
    REDIS_URL: requiredURL('REDIS_URL'),
    PDF_EDIT_API_URL: requiredURL('PDF_EDIT_API_URL'),
    SERVICE_HOST_SMS: requiredURL('SERVICE_HOST_SMS'),
    SERVICE_SMS_API_KEY: requiredString('SERVICE_SMS_API_KEY'),
    RECAPTCHA_SECRET_KEY: requiredString('RECAPTCHA_SECRET_KEY'),
  }),
  sepay: z.object({
    SEPAY_HUB_API_KEY: requiredString('SEPAY_HUB_API_KEY'),
    SEPAY_HUB_CLIENT_ID: requiredString('SEPAY_HUB_CLIENT_ID'),
    SEPAY_HUB_CLIENT_SECRET: requiredString('SEPAY_HUB_CLIENT_SECRET'),
    SEPAY_HUB_API_URL: requiredURL('SEPAY_HUB_API_URL'),
    SEPAY_WEBHOOK_API_KEY: requiredString('SEPAY_WEBHOOK_API_KEY'),
  }),
  swagger: z.object({
    SWAGGER_LOCAL_URL: requiredURL('SWAGGER_LOCAL_URL'),
    SWAGGER_DEV_URL: requiredURL('SWAGGER_DEV_URL'),
    SWAGGER_TEST_URL: requiredURL('SWAGGER_TEST_URL'),
    SWAGGER_STAGING_URL: requiredURL('SWAGGER_STAGING_URL'),
    SWAGGER_PROD_URL: requiredURL('SWAGGER_PROD_URL'),
  }),
  zalo: z.object({
    ZALO_APP_ID: requiredString('ZALO_APP_ID'),
    ZALO_APP_SECRET: requiredString('ZALO_APP_SECRET'),
    ZALO_WEBHOOK_SECRET: requiredString('ZALO_WEBHOOK_SECRET'),
    ZALO_WEBHOOK_URL: requiredURL('ZALO_WEBHOOK_URL'),
  }),
  google: z.object({
    GOOGLE_CLIENT_ID: requiredString('GOOGLE_CLIENT_ID'),
    GOOGLE_CLIENT_SECRET: requiredString('GOOGLE_CLIENT_SECRET'),
    GOOGLE_REDIRECT_URI: requiredURL('GOOGLE_REDIRECT_URI'),
    GOOGLE_APPLICATION_CREDENTIALS: requiredString(
      'GOOGLE_APPLICATION_CREDENTIALS',
    ),
    GOOGLE_CLOUD_PROJECT_ID: requiredString('GOOGLE_CLOUD_PROJECT_ID'),
    GOOGLE_CLOUD_STORAGE_BUCKET: requiredString('GOOGLE_CLOUD_STORAGE_BUCKET'),
    GOOGLE_ADS_CLIENT_ID: requiredString('GOOGLE_ADS_CLIENT_ID'),
    GOOGLE_ADS_CLIENT_SECRET: requiredString('GOOGLE_ADS_CLIENT_SECRET'),
    GOOGLE_ADS_DEVELOPER_TOKEN: requiredString('GOOGLE_ADS_DEVELOPER_TOKEN'),
    GOOGLE_ADS_REFRESH_TOKEN: requiredString('GOOGLE_ADS_REFRESH_TOKEN'),
    GOOGLE_ADS_LOGIN_CUSTOMER_ID: requiredString(
      'GOOGLE_ADS_LOGIN_CUSTOMER_ID',
    ),
  }),
  facebook: z.object({
    FACEBOOK_APP_ID: requiredString('FACEBOOK_APP_ID'),
    FACEBOOK_APP_SECRET: requiredString('FACEBOOK_APP_SECRET'),
    FACEBOOK_REDIRECT_URI: requiredURL('FACEBOOK_REDIRECT_URI'),
    FACEBOOK_GRAPH_API_VERSION: requiredString('FACEBOOK_GRAPH_API_VERSION'),
  }),
  agent: z.object({
    AGENT_API_KEY: requiredString('AGENT_API_KEY'),
    ENCRYPTION_SECRET_KEY: requiredString('ENCRYPTION_SECRET_KEY'),
    API_PREFIX_KEY: requiredString('API_PREFIX_KEY'),
    USER_SECRET_MODEL: requiredString('USER_SECRET_MODEL'),
    ADMIN_SECRET_MODEL: requiredString('ADMIN_SECRET_MODEL'),
    API_SECRET_KEY: requiredString('API_SECRET_KEY'),
    MCP_HOST: requiredString('MCP_HOST'),
    MCP_PORT: requiredNumber('MCP_PORT'),
  }),
  email: z.object({
    MAIL_HOST: requiredString('MAIL_HOST'),
    MAIL_PORT: requiredNumber('MAIL_PORT'),
    MAIL_SECURE: z.string().transform((v) => v === 'true'),
    MAIL_USERNAME: requiredString('MAIL_USERNAME'),
    MAIL_PASSWORD: requiredString('MAIL_PASSWORD'),
    MAIL_DEFAULT_FROM: requiredString('MAIL_DEFAULT_FROM'),
  }),
  fpt: z.object({
    FPT_SMS_CLIENT_ID: requiredString('FPT_SMS_CLIENT_ID'),
    FPT_SMS_CLIENT_SECRET: requiredString('FPT_SMS_CLIENT_SECRET'),
    FPT_SMS_SCOPE: requiredString('FPT_SMS_SCOPE'),
    FPT_SMS_API_URL: requiredURL('FPT_SMS_API_URL'),
    FPT_SMS_BRANDNAME: requiredString('FPT_SMS_BRANDNAME'),
  }),
  llmSystemEncryptionKey: z.object({
    USER_SECRECT_MODEL: requiredString('USER_SECRECT_MODEL'),
    ADMIN_SECRECT_MODEL: requiredString('ADMIN_SECRECT_MODEL'),
  })
});

let env: z.infer<typeof envSchema>;

try {
  env = envSchema.parse({
    misc: process.env,
    app: process.env,
    database: process.env,
    s3: process.env,
    cdn: process.env,
    openai: process.env,
    external: process.env,
    sepay: process.env,
    swagger: process.env,
    zalo: process.env,
    google: process.env,
    facebook: process.env,
    agent: process.env,
    email: process.env,
    fpt: process.env,
    llmSystemEncryptionKey: process.env,
  });
} catch (err: unknown) {
  if (err instanceof ZodError) {
    logger.error(
      '❌ Invalid environment variables:',
      JSON.stringify(err.format(), null, 2),
    );
    process.exit(1);
  }
  throw err;
}

export { env };
