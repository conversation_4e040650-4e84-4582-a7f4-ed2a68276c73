import { Global, Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { QueueName } from './index';
import { BullBoardModule } from '@bull-board/nestjs';
import { ExpressAdapter } from '@bull-board/express';
import basicAuth from 'express-basic-auth';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';

@Global()
@Module({
  imports: [
    BullModule.registerQueue({
      name: QueueName.EMAIL_SYSTEM,
    }),
    BullModule.registerQueue({
      name: QueueName.EMAIL_MARKETING,
    }),
    BullModule.registerQueue({
      name: QueueName.SMS,
    }),
    BullBoardModule.forRoot({
      route: '/queues',
      adapter: ExpressAdapter,
      middleware: basicAuth({
        challenge: true,
        users: { admin: 'redai@123' },
      }),
    }),
    BullBoardModule.forFeature({
      name: QueueName.EMAIL_SYSTEM,
      adapter: BullMQAdapter,
    }),
    BullBoardModule.forFeature({
      name: QueueName.EMAIL_MARKETING,
      adapter: BullMQAdapter,
    }),
    BullBoardModule.forFeature({
      name: QueueName.SMS,
      adapter: BullMQAdapter,
    }),
  ],
})
export class QueueModule {}
