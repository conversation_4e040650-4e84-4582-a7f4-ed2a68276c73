# Email Marketing Module

Module xử lý email marketing với queue, tracking và template variables.

## Tính năng

- ✅ Xử lý queue email marketing từ Redis
- ✅ Inject biến tùy chỉnh vào template ({{variable}})
- ✅ Pixel tracking 1x1 cho email opened
- ✅ Lưu tracking data tạm vào Redis
- ✅ Batch save tracking data vào database
- ✅ Support custom email server config
- ✅ Retry mechanism cho failed emails

## Cấu trúc

```
src/modules/marketing/email/
├── dto/                          # Data Transfer Objects
│   ├── email-marketing-job.dto.ts
│   ├── email-tracking.dto.ts
│   └── index.ts
├── services/                     # Business Logic Services
│   ├── email-marketing.service.ts
│   ├── email-template.service.ts
│   ├── email-tracking.service.ts
│   └── index.ts
├── email-marketing.processor.ts  # Queue Processor
├── email-tracking.controller.ts  # Tracking Endpoints
├── email-marketing.controller.ts # Management Endpoints
├── email-marketing.module.ts     # NestJS Module
└── index.ts
```

## Cách sử dụng

### 1. Tạo Campaign Email

Tạo record trong bảng `user_campaigns` với:
- `platform`: 'email'
- `subject`: Tiêu đề email (có thể chứa {{variable}})
- `content`: Nội dung HTML (có thể chứa {{variable}})
- `audienceIds`: Array ID của audiences
- `server`: Cấu hình email server (optional)

### 2. Tạo Jobs cho Campaign

```bash
POST /api/email-marketing/campaigns/{campaignId}/jobs
```

### 3. Theo dõi Queue

```bash
GET /api/email-marketing/queue/status
```

### 4. Xem thống kê Campaign

```bash
GET /api/email-marketing/campaigns/{campaignId}/stats
```

### 5. Hủy Jobs

```bash
DELETE /api/email-marketing/campaigns/{campaignId}/jobs
```

## Template Variables

### Cú pháp

Sử dụng `{{variable}}` trong subject và content:

```html
<h1>Xin chào {{name}}!</h1>
<p>Email của bạn: {{email}}</p>
<p>Địa chỉ: {{profile.address.city}}</p>
```

### Custom Fields

Biến được lấy từ bảng `user_audience_custom_fields`:
- `fieldName`: Tên biến
- `fieldValue`: Giá trị biến

## Tracking

### Email Opened Tracking

Tự động inject pixel tracking:
```html
<img src="/api/email-tracking/pixel/{trackingId}" width="1" height="1" style="display:none;" />
```

### Click Tracking (Optional)

Wrap links với tracking URL:
```html
<a href="/api/email-tracking/click/{trackingId}?url=https://example.com">Click here</a>
```

### Tracking Data Flow

1. **Email Sent** → Redis (immediate)
2. **Email Opened** → Redis (immediate)
3. **Batch Processor** → Database (every 30s)

## Queue Configuration

Queue name: `EMAIL_MARKETING`

### Job Options
- **Concurrency**: 10
- **Retry**: 3 attempts
- **Backoff**: Exponential (2s base)
- **Delay**: Support scheduled emails

## Database Schema

### Tracking History

Lưu vào `user_campaign_history`:
- `campaignId`: ID campaign
- `audienceId`: ID audience
- `status`: 'sent', 'opened', 'clicked', 'failed'
- `sentAt`: Timestamp
- `createdAt`: Timestamp

## Environment Variables

```env
# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-password
EMAIL_FROM=<EMAIL>

# Application
BASE_URL=http://localhost:3000

# Redis
REDIS_URL=redis://localhost:6379
```

## API Endpoints

### Management
- `POST /api/email-marketing/campaigns/{id}/jobs` - Tạo jobs
- `DELETE /api/email-marketing/campaigns/{id}/jobs` - Hủy jobs
- `GET /api/email-marketing/campaigns/{id}/stats` - Thống kê
- `GET /api/email-marketing/queue/status` - Trạng thái queue

### Tracking
- `GET /api/email-tracking/pixel/{trackingId}` - Pixel tracking
- `GET /api/email-tracking/click/{trackingId}?url=...` - Click tracking

## Monitoring

### Bull Dashboard
Truy cập: `http://localhost:3000/queues`
- Username: `admin`
- Password: `redai@123`

### Logs
- Service logs: `EmailMarketingService`
- Processor logs: `EmailMarketingProcessor`
- Tracking logs: `EmailTrackingService`

## Troubleshooting

### Common Issues

1. **Jobs không được tạo**
   - Kiểm tra campaign có `platform = 'email'`
   - Kiểm tra `audienceIds` có hợp lệ
   - Xem logs trong `EmailMarketingService`

2. **Email không gửi được**
   - Kiểm tra cấu hình SMTP
   - Xem logs trong `EmailMarketingProcessor`
   - Kiểm tra queue failed jobs

3. **Tracking không hoạt động**
   - Kiểm tra `BASE_URL` environment
   - Xem logs trong `EmailTrackingService`
   - Kiểm tra Redis connection

### Debug Commands

```bash
# Xem queue status
curl http://localhost:3000/api/email-marketing/queue/status

# Xem failed jobs trong Bull Dashboard
# http://localhost:3000/queues

# Check Redis keys
redis-cli keys "email_tracking:*"
```
