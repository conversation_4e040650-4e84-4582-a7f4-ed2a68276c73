import { Injectable, Logger } from '@nestjs/common';
import { z } from 'zod';
import { UserAgentRunsQueries } from './user-agent-runs.queries';
import { UserMessagesQueries } from './user-messages.queries';
import { UserUsageQueries } from './user-usage.queries';
import { RedisService } from '../../../../infra';
import { REDIS_EVENTS, RunCancelEvent, RunTriggerEvent } from '../../constants';
import { EmitEventCallback, TransformedEvent } from '../interfaces/event';
import { UserAgentRunStatus } from '../../enums';
import {
  LangGraphEventProcessorService,
  StreamingSetupService,
  ThreadCompletionHandlerService,
  ThreadConfigurationService,
} from './index';
import { CompletionContext } from '../interfaces';

// Zod schemas for validation
const RunTriggerEventSchema = z.object({
  runId: z.string(),
  threadId: z.string(),
  agentId: z.string(),
  userId: z.number(),
  jwt: z.string(),
  timestamp: z.number(),
  eventType: z.literal(REDIS_EVENTS.RUN_TRIGGER),
});

const RunCancelEventSchema = z.object({
  threadId: z.string(),
  reason: z.string(),
  timestamp: z.number(),
  eventType: z.literal(REDIS_EVENTS.RUN_CANCEL),
  runId: z.string().optional(), // Optional as per interface inconsistency
});

@Injectable()
export class AgentSystemService {
  // ✅ FIXED: Use threadId as key (matches LangGraph execution context)
  private readonly activeThreads = new Map<string, AbortController>(); // threadId → AbortController
  private readonly logger = new Logger(AgentSystemService.name);

  constructor(
    private readonly userAgentRunsQueries: UserAgentRunsQueries,
    private readonly userMessagesQueries: UserMessagesQueries,
    private readonly userUsageQueries: UserUsageQueries,
    private readonly redisService: RedisService,
    private readonly threadConfigurationService: ThreadConfigurationService,
    private readonly streamingSetupService: StreamingSetupService,
    private readonly eventProcessorService: LangGraphEventProcessorService,
    private readonly completionHandlerService: ThreadCompletionHandlerService,
  ) {}

  async triggerRun(data: RunTriggerEvent): Promise<void> {
    try {
      this.logger.log(
        `Received run trigger event for thread ${data.threadId}, run ${data.runId}`,
      );

      // 🔍 DEBUG: Log detailed trigger event info
      this.logger.debug(`🔍 WORKER TRIGGER - Detailed event info:`, {
        threadId: data.threadId,
        runId: data.runId,
        agentId: data.agentId,
        userId: data.userId,
        timestamp: data.timestamp,
        eventType: data.eventType,
        currentActiveThreadsCount: this.activeThreads.size,
        currentActiveThreadIds: Array.from(this.activeThreads.keys()),
        hasThisThreadActive: this.activeThreads.has(data.threadId),
      });

      // Validate event payload
      if (!this.validateRunTriggerEvent(data)) {
        this.logger.error(
          `Invalid run trigger event payload for thread ${data?.['threadId']}`,
        );
        return;
      }

      // ✅ SIMPLE APPROACH: AbortController is registered early, cancellation will be detected
      // in processAgentThread and during LangGraph streaming

      // ✅ NEW: Check if thread is already being processed using database
      const isActive = await this.userAgentRunsQueries.isThreadActive(
        data.threadId,
      );
      if (isActive) {
        this.logger.warn(
          `Thread ${data.threadId} is already being processed, ignoring duplicate trigger`,
        );
        return;
      }

      // 🔑 EARLY REGISTRATION: Create and register AbortController IMMEDIATELY
      // This allows cancellation even before any async operations start
      const abortController = new AbortController();
      this.activeThreads.set(data.threadId, abortController);

      // 🔍 DEBUG: Log AbortController storage details
      this.logger.debug(`🔑 STORED AbortController for cancellation:`, {
        threadId: data.threadId,
        runId: data.runId,
        activeThreadsSize: this.activeThreads.size,
        allActiveThreadIds: Array.from(this.activeThreads.keys()),
        storedSuccessfully: this.activeThreads.has(data.threadId),
      });

      // Fetch run data from database
      const runData = await this.userAgentRunsQueries.getRunById(data.runId);

      if (!runData) {
        this.logger.error(
          `Run ${data.runId} not found in database for thread ${data.threadId}`,
        );
        return;
      }

      // ✅ NEW: Mark run as active in database
      await this.userAgentRunsQueries.updateRunStatus(
        data.runId,
        UserAgentRunStatus.RUNNING,
      );

      this.logger.log(
        `Processing thread ${data.threadId} for agent ${data.agentId} (run ${data.runId}) with JWT`,
      );

      await this.processAgentThread(
        data.threadId,
        runData,
        abortController,
        data.jwt,
      );
    } catch (error) {
      // ✅ REFACTORED: Use reusable abort error detection
      if (this.isAbortError(error)) {
        await this.handleAbortError(
          error,
          data.threadId,
          data.runId,
          'during trigger',
        );
      } else {
        this.logger.error(
          `Error handling run trigger for thread ${data.threadId}:`,
          {
            message: error.message,
            stack: error.stack,
            name: error.name,
            threadId: data.threadId,
            runId: data.runId,
            error: error,
          },
        );

        // ✅ Update status to FAILED for non-abort errors
        try {
          await this.userAgentRunsQueries.updateRunStatus(
            data.runId,
            UserAgentRunStatus.FAILED,
          );
          this.logger.debug(
            `✅ Updated run ${data.runId} status to FAILED due to trigger error`,
          );
        } catch (statusError) {
          this.logger.error(`❌ Failed to update run status to FAILED:`, {
            runId: data.runId,
            threadId: data.threadId,
            error: statusError.message,
          });
        }
      }
    } finally {
      // 🧹 ALWAYS perform final cleanup regardless of success/error/cancellation
      this.logger.debug(`🧹 Performing final cleanup for ${data.threadId}`);
      try {
        await this.cleanupRun(data.threadId, data.runId);
      } catch (cleanupError) {
        this.logger.error(
          `💥 Failed final cleanup for ${data.threadId}:`,
          cleanupError,
        );
      }
    }
  }

  async cancelRun(data: RunCancelEvent): Promise<void> {
    try {
      this.logger.log(`
        ------------------------------------------------------------------------------
        Received run cancel event for thread ${data.threadId}: ${data.reason}
        ------------------------------------------------------------------------------
        `);

      // Validate event payload
      if (!this.validateRunCancelEvent(data)) {
        this.logger.error(
          `Invalid run cancel event payload for thread ${data?.['threadId']}`,
        );
        return;
      }

      // ✅ NEW: Get runId from database if not provided
      let runId: string = data.runId || '';
      if (!data.runId) {
        const foundRunId = await this.userAgentRunsQueries.getRunIdByThreadId(
          data.threadId,
        );
        if (!foundRunId) {
          this.logger.log(
            `No active run found for thread ${data.threadId}, ignoring cancel request`,
          );
          return;
        }
        runId = foundRunId;
      }

      // ✅ NEW: Check if run is active using database
      const activeRun = await this.userAgentRunsQueries.getActiveRunByThreadId(
        data.threadId,
      );
      if (!activeRun) {
        this.logger.log(
          `Thread ${data.threadId} is not active, ignoring cancel request (this is normal)`,
        );
        return;
      }

      // 🔍 DEBUG: Log AbortController lookup details BEFORE attempting to get it
      this.logger.debug(`🔍 LOOKING UP AbortController for cancellation:`, {
        lookupThreadId: data.threadId,
        lookupRunId: runId,
        activeThreadsSize: this.activeThreads.size,
        allActiveThreadIds: Array.from(this.activeThreads.keys()),
        hasExactMatch: this.activeThreads.has(data.threadId),
        threadIdType: typeof data.threadId,
        threadIdLength: data.threadId?.length,
      });

      // ✅ FIXED: Get AbortController by threadId (matches LangGraph execution context)
      const abortController = this.activeThreads.get(data.threadId);
      let shouldEmitSessionEnd: boolean;

      if (abortController) {
        // Abort the thread using LangGraph AbortController pattern
        abortController.abort();
        this.logger.log(
          `✅ Cancelled thread ${data.threadId} (run ${runId}): ${data.reason}`,
        );

        // 🔍 DEBUG: Confirm successful cancellation
        this.logger.debug(`🎯 SUCCESSFUL CANCELLATION:`, {
          threadId: data.threadId,
          runId: runId,
          abortSignalAborted: abortController.signal.aborted,
          reason: data.reason,
        });

        // ✅ AbortController found: handleFinalCleanup() will emit stream_session_end
        shouldEmitSessionEnd = false;
      } else {
        this.logger.warn(
          `⚠️ No AbortController found for thread ${data.threadId} (run ${runId})`,
        );

        // 🔍 DEBUG: Detailed mismatch analysis
        this.logger.error(`🚨 THREADID MISMATCH ANALYSIS:`, {
          cancelRequestThreadId: data.threadId,
          cancelRequestRunId: runId,
          activeThreadsCount: this.activeThreads.size,
          storedThreadIds: Array.from(this.activeThreads.keys()),
          exactMatches: Array.from(this.activeThreads.keys()).filter(
            (id) => id === data.threadId,
          ),
          similarMatches: Array.from(this.activeThreads.keys()).filter(
            (id) =>
              id.includes(data.threadId.substring(0, 8)) ||
              data.threadId.includes(id.substring(0, 8)),
          ),
          threadIdComparison: Array.from(this.activeThreads.keys()).map(
            (storedId) => ({
              stored: storedId,
              requested: data.threadId,
              equal: storedId === data.threadId,
              storedLength: storedId.length,
              requestedLength: data.threadId.length,
            }),
          ),
        });

        // ✅ No AbortController: we need to emit stream_session_end here
        shouldEmitSessionEnd = true;
      }

      // ✅ FIXED: Update status to CANCELLED (completion handler only runs if AbortController exists)
      try {
        await this.userAgentRunsQueries.updateRunStatus(
          runId,
          UserAgentRunStatus.CANCELLED,
        );
        this.logger.debug(`✅ Updated run ${runId} status to CANCELLED`);
      } catch (statusError) {
        this.logger.error(`❌ Failed to update run status to CANCELLED:`, {
          runId,
          threadId: data.threadId,
          error: statusError.message,
        });
      }

      // ✅ FIXED: Only emit stream_session_end if no AbortController (avoids duplicate)
      if (shouldEmitSessionEnd) {
        try {
          const producer = this.redisService.getRawClient();
          await this.emitEvent(producer, data.threadId, runId, {
            type: 'stream_session_end',
            data: { reason: data.reason, endTime: Date.now() },
          });
          this.logger.debug(
            `📡 Emitted stream_session_end for cancelled thread ${data.threadId} (no active processing)`,
          );
        } catch (error) {
          this.logger.warn(
            `Failed to emit cancellation event for thread ${data.threadId}:`,
            error,
          );
        }
      } else {
        this.logger.debug(
          `📡 Skipping stream_session_end emission - handleFinalCleanup() will emit it`,
        );
      }

      // ✅ FIXED: Cleanup using threadId first
      await this.cleanupRun(data.threadId, runId);
    } catch (error) {
      this.logger.error(
        `Error handling run cancel for thread ${data.threadId}:`,
        error,
      );
    }
  }

  /**
   * Validate run trigger event payload using Zod
   * @param data Event data to validate
   * @returns True if valid
   */
  private validateRunTriggerEvent(data: any): data is RunTriggerEvent {
    try {
      RunTriggerEventSchema.parse(data);
      return true;
    } catch (error) {
      this.logger.debug('RunTriggerEvent validation failed:', {
        error: error instanceof z.ZodError ? error.errors : error.message,
        receivedData: data,
      });
      return false;
    }
  }

  /**
   * Validate run cancel event payload using Zod
   * @param data Event data to validate
   * @returns True if valid
   */
  private validateRunCancelEvent(data: any): data is RunCancelEvent {
    try {
      RunCancelEventSchema.parse(data);
      return true;
    } catch (error) {
      this.logger.debug('RunCancelEvent validation failed:', {
        error: error instanceof z.ZodError ? error.errors : error.message,
        receivedData: data,
      });
      return false;
    }
  }

  /**
   * Check if an error is an abort/cancellation error
   * @param error Error to check
   * @param abortController AbortController to check signal
   * @returns True if this is an abort error
   */
  private isAbortError(
    error: Error,
    abortController?: AbortController,
  ): boolean {
    return (
      error.name === 'AbortError' ||
      (abortController?.signal.aborted ?? false) ||
      error.message.includes('cancelled') ||
      error.message.includes('aborted')
    );
  }

  /**
   * Handle abort error with proper status update and logging
   * @param error The abort error
   * @param threadId Thread ID
   * @param runId Run ID
   * @param context Context description (e.g., "during trigger", "during processing")
   */
  private async handleAbortError(
    error: Error,
    threadId: string,
    runId: string,
    context: string,
  ): Promise<void> {
    this.logger.log(`🛑 Thread ${threadId} was cancelled ${context}:`, {
      threadId,
      runId,
      errorMessage: error.message,
      context,
    });

    // ✅ Update status to CANCELLED for abort errors
    try {
      await this.userAgentRunsQueries.updateRunStatus(
        runId,
        UserAgentRunStatus.CANCELLED,
      );
      this.logger.debug(
        `✅ Updated run ${runId} status to CANCELLED due to cancellation ${context}`,
      );
    } catch (statusError) {
      this.logger.error(`❌ Failed to update run status to CANCELLED:`, {
        runId,
        threadId,
        context,
        error: statusError.message,
      });
    }
  }

  /**
   * Process agent thread with LangGraph using refactored service architecture
   * @param threadId LangGraph thread ID
   * @param runData Run data from database
   * @param abortController AbortController for cancellation
   * @param jwt JWT token for authenticated API calls
   */
  private async processAgentThread(
    threadId: string,
    runData: any,
    abortController: AbortController,
    jwt: string,
  ): Promise<void> {
    this.logger.debug(
      `🚀 Processing LangGraph thread ${threadId} with refactored service architecture:`,
      {
        threadId,
        runId: runData.id,
        status: runData.status,
        payloadSize: JSON.stringify(runData.payload).length,
        aborted: abortController.signal.aborted,
        hasJwt: !!jwt,
        jwtLength: jwt.length,
      },
    );

    // Declare variables outside try block for finally access
    let streamingComponents: any = null;
    let emitEventCallback: EmitEventCallback | undefined = undefined;
    let processingError: Error | undefined = undefined;
    let completionContext: CompletionContext | undefined = undefined;
    try {
      // ✅ EARLY CANCELLATION CHECK: If cancelled before processing starts
      if (abortController.signal.aborted) {
        this.logger.log(
          `🛑 Thread ${threadId} was cancelled before processing started`,
        );
        const abortError = new Error(
          `Thread ${threadId} was cancelled before processing`,
        );
        abortError.name = 'AbortError';
        throw abortError;
      }

      // 🔧 STEP 1: Build thread configuration using ThreadConfigurationService
      this.logger.debug(
        `📋 Step 1: Building thread configuration for ${threadId}`,
      );
      const threadConfig =
        await this.threadConfigurationService.buildConfiguration(
          runData,
          threadId,
          jwt,
        );

      // 🔗 STEP 2: Create the real emitEvent callback first
      this.logger.debug(`🔗 Step 2: Creating event callback for ${threadId}`);
      emitEventCallback = async (event: TransformedEvent) => {
        // Note: producer will be available after setupComponents
        await this.emitEvent(
          this.redisService.getRawClient(),
          threadId,
          runData.id,
          event,
        );
      };
      this.logger.debug(`🔗 Created event callback for ${threadId}`);

      // 🛠️ STEP 3: Setup streaming components with everything needed from start
      this.logger.debug(
        `⚙️ Step 3: Setting up streaming components for ${threadId}`,
      );
      streamingComponents = await this.streamingSetupService.setupComponents(
        threadConfig,
        emitEventCallback,
        abortController,
      );

      completionContext = this.completionHandlerService.createCompletionContext(
        threadId,
        runData,
        streamingComponents.partialTokens,
        abortController,
        emitEventCallback,
        threadConfig.customConfig.checkpoint_id,
      );

      // 📡 STEP 4: Process events using LangGraphEventProcessorService (was Step 5)
      this.logger.debug(`📡 Step 4: Starting event processing for ${threadId}`);
      const baseContext = this.eventProcessorService.createBaseContext(
        streamingComponents,
        threadConfig,
        abortController,
      );

      // Add missing dependencies to base context
      const completeContext = {
        ...baseContext,
        userMessagesQueries: this.userMessagesQueries,
        userUsageQueries: this.userUsageQueries,
      };

      const processingResult = await this.eventProcessorService.processEvents(
        streamingComponents.streamingIterator,
        completeContext,
      );

      // 🏁 STEP 5: Handle completion based on processing result
      this.logger.debug(`🏁 Step 5: Handling completion for ${threadId}`, {
        completed: processingResult.completed,
        cancelled: processingResult.cancelled,
        hasError: !!processingResult.error,
      });

      if (processingResult.error) {
        await this.completionHandlerService.handleErrorCompletion(
          completionContext,
          processingResult.error,
        );
      } else if (processingResult.completed && !processingResult.cancelled) {
        await this.completionHandlerService.handleSuccessfulCompletion(
          completionContext,
        );
      }

      this.logger.log(
        `✅ Successfully completed processing for thread ${threadId}`,
        {
          threadId,
          runId: runData.id,
          completed: processingResult.completed,
          cancelled: processingResult.cancelled,
          totalTokens: streamingComponents.partialTokens.length,
        },
      );
    }
    catch (error) {
      // Store error for finally block
      processingError = error;

      // ✅ REFACTORED: Use reusable abort error detection
      if (this.isAbortError(error, abortController)) {
        this.logger.log(
          `🛑 Thread ${threadId} was cancelled during setup/processing:`,
          {
            threadId,
            runId: runData.id,
            errorMessage: error.message,
            abortSignalAborted: abortController.signal.aborted,
          },
        );
        // Note: handleFinalCleanup will handle cancellation cleanup in finally block
      }
      else {
        this.logger.error(
          `💥 Fatal error in processAgentThread for ${threadId}:`,
          {
            threadId,
            runId: runData.id,
            error: error.message,
            stack: error.stack,
          },
        );

        // 🚨 Emit stream error event before cleanup
        try {
          // Use Redis service directly since streamingComponents might not be initialized
          const streamKey = `agent_stream:${threadId}:${runData.id}`;

          await this.redisService.xadd(streamKey, {
            event: 'stream_error',
            data: JSON.stringify({
              error: error.message || 'Unknown streaming error',
              errorName: error.name,
              stack: error.stack,
              threadId: threadId,
              runId: runData.id,
              timestamp: Date.now(),
            }),
            timestamp: Date.now().toString(),
          });

          // Publish notification to wake up subscribers
          await this.redisService.getRawClient().publish(streamKey, '');

          this.logger.debug(
            `📡 Emitted stream_error event for fatal error in ${threadId}`,
          );
        }
        catch (emitError) {
          this.logger.error(
            `Failed to emit stream_error event for thread ${threadId}:`,
            {
              threadId,
              runId: runData.id,
              emitError: emitError.message,
            },
          );
        }
        // Note: handleFinalCleanup will handle error cleanup in finally block
      }

      // Don't re-throw to prevent bubbling up to event handler
    }
    finally {
      // 🧹 ALWAYS perform final cleanup regardless of success/error/cancellation
      this.logger.debug(`🧹 Performing final cleanup for ${threadId}`);
      try {
        if (completionContext) {
          this.logger.log(
            `completion context found: ${completionContext.threadId}`,
          );
          // Pass error to handleFinalCleanup for proper error handling
          await this.completionHandlerService.handleFinalCleanup(
            completionContext,
            processingError,
          );
        }
      } catch (cleanupError) {
        this.logger.error(
          `💥 Failed final cleanup for ${threadId}:`,
          cleanupError,
        );
      }
    }
  }

  /**
   * ✅ FIXED: Cleanup run resources using database and threadId
   * @param threadId Thread ID (primary key for AbortController)
   * @param runId Run ID (for database operations)
   */
  private async cleanupRun(threadId: string, runId: string): Promise<void> {
    try {
      // ✅ FIXED: Remove AbortController from memory using threadId
      this.activeThreads.delete(threadId);

      // ✅ FIXED: Don't update status here - completion handlers manage status
      // cleanupRun() only removes AbortController, status is managed by completion handlers
      this.logger.debug(`✅ Removed AbortController for thread ${threadId}`);

      this.logger.debug(`✅ Cleaned up thread ${threadId} (run ${runId})`);
    } catch (error) {
      this.logger.error(`❌ Error cleaning up thread ${threadId}:`, {
        threadId,
        runId,
        error: error.message,
      });
    }
  }

  /**
   * Type-safe event emission to Redis Streams for backend SSE consumption
   * Ensures all emitted events conform to the TransformedEvent union type
   *
   * @param producer Redis client for publishing
   * @param threadId Thread ID for the event
   * @param runId Run ID for the event
   * @param event Type-safe event object conforming to TransformedEvent
   *
   * @example
   * ```typescript
   * await this.emitEvent(producer, threadId, runId, {
   *   type: 'update_rpoint',
   *   data: { rPointCost: 10, updatedBalance: 100 }
   * });
   * ```
   */
  private async emitEvent(
    producer: any,
    threadId: string,
    runId: string,
    event: TransformedEvent,
  ): Promise<void> {
    try {
      const streamKey = `agent_stream:${threadId}:${runId}`;

      // Use the working implementation's format: direct object to xadd
      await this.redisService.xadd(streamKey, {
        event: event.type,
        data: JSON.stringify(event.data),
        timestamp: Date.now().toString(),
      });

      // Publish notification to wake up subscribers (simplified notification)
      await producer.publish(streamKey, '');

      this.logger.debug(
        `Emitted typed event ${event.type} for thread ${threadId}`,
        {
          threadId,
          runId,
          eventType: event.type,
          dataKeys: Object.keys(event.data || {}),
        },
      );
    } catch (error) {
      this.logger.error(
        `Failed to emit typed event ${event.type} for thread ${threadId}:`,
        {
          threadId,
          runId,
          eventType: event.type,
          error: error.message,
          stack: error.stack,
        },
      );
      throw error;
    }
  }
}
