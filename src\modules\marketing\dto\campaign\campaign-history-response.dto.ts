import { ApiProperty } from '@nestjs/swagger';

/**
 * Enum cho các trạng thái gửi
 */
export enum SendStatus {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  FAILED = 'failed',
  OPENED = 'opened',
  CLICKED = 'clicked',
}

/**
 * DTO cho phản hồi thông tin lịch sử campaign
 */
export class CampaignHistoryResponseDto {
  /**
   * ID của lịch sử
   * @example 1
   */
  @ApiProperty({
    description: 'ID của lịch sử',
    example: 1,
  })
  id: number;

  /**
   * ID của campaign
   * @example 1
   */
  @ApiProperty({
    description: 'ID của campaign',
    example: 1,
  })
  campaignId: number;

  /**
   * ID của audience
   * @example 1
   */
  @ApiProperty({
    description: 'ID của audience',
    example: 1,
  })
  audienceId: number;

  /**
   * Trạng thái gửi
   * @example "sent"
   */
  @ApiProperty({
    description: 'Tr<PERSON>ng thái gửi',
    enum: SendStatus,
    example: SendStatus.SENT,
  })
  status: SendStatus;

  /**
   * Thời gian gửi (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian gửi (Unix timestamp)',
    example: 1619171200,
  })
  sentAt: number;

  /**
   * Thời gian tạo (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1619171200,
  })
  createdAt: number;
}
