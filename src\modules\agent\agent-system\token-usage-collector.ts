// src/callbacks/TokenUsageCollector.ts
import { BaseCallbackHandler, HandleLLMNewTokenCallbackFields, NewTokenIndices } from '@langchain/core/callbacks/base';
import { LLMResult } from '@langchain/core/outputs';
import { Logger } from '@nestjs/common';
import { SystemAgentConfigMap } from './interfaces';
import { UserUsageQueries } from './services/user-usage.queries';
import { EmitEventCallback } from './interfaces/event';

const logger = new Logger('TokenUsageCollector');

export interface DetailedUsage {
  agentId: string;
  model: string | null;
  inputTokens: number;
  outputTokens: number;
  totalTokens: number;
  pointCost: number;
}

export class TokenUsageCollector extends BaseCallbackHandler {
  name = 'TokenUsageCollector';
  private usages: DetailedUsage[] = [];

  constructor(
    private readonly agentConfigMap: SystemAgentConfigMap,
    private readonly userId: number,
    private readonly threadId: string,
    private readonly runId: string,
    private readonly userUsageQueries: UserUsageQueries,
    private readonly emitEventCallback: EmitEventCallback,
  ) {
    super();

    // Validate required parameters
    if (!agentConfigMap) {
      throw new Error('agentConfigMap is required');
    }
    if (!userId || typeof userId !== 'number') {
      throw new Error('userId must be a valid number');
    }
    if (!threadId || typeof threadId !== 'string') {
      throw new Error('threadId must be a valid string');
    }
    if (!runId || typeof runId !== 'string') {
      throw new Error('runId must be a valid string');
    }
    if (!userUsageQueries) {
      throw new Error('userUsageQueries is required');
    }
    if (!emitEventCallback || typeof emitEventCallback !== 'function') {
      throw new Error('emitEventCallback must be a valid function');
    }
  }

  public getUsages(): DetailedUsage[] {
    return this.usages;
  }

  public getTotalCost(): number {
    return this.usages.reduce(
      (totalCost, currentUsage) => totalCost + currentUsage.pointCost,
      0,
    );
  }

  override async handleLLMEnd(
    output: LLMResult,
    _runId: string,
    _parentRunId?: string,
    tags?: string[],
  ): Promise<void> {
    // for most chat models `output.llmOutput.tokenUsage` holds the counts
    logger.debug(`🎯 TokenUsageCollector.handleLLMEnd called for thread ${this.threadId}`, {
      threadId: this.threadId,
      runId: this.runId,
      userId: this.userId,
      tags: tags || [],
      hasOutput: !!output,
      hasLLMOutput: !!output.llmOutput,
      hasTokenUsage: !!output.llmOutput?.tokenUsage,
    });

    logger.debug(`output = ${JSON.stringify(output, null, 2)}`);
    const tokenUsage = output.llmOutput?.tokenUsage;
    if (!tokenUsage) {
      logger.debug(`No token usage data in LLMEnd event. Skipping.`);
      return;
    }
    const agentTag = tags?.find((t) => t.startsWith('agent_id:'));
    if (!agentTag) {
      logger.warn(
        `No 'agent_id' tag found in LLM call. Skipping cost calculation.`,
        { _runId, tags },
      );
      return;
    }
    // Extract the agentId from the tag 'agent_id:some-agent-123'
    const agentId = agentTag.split(':')[1];
    const agentConfig = this.agentConfigMap[agentId];
    if (!agentConfig) {
      logger.error(
        `Could not find configuration for tagged agentId: ${agentId}. Skipping cost calculation.`,
      );
      return;
    }
    let pointCost = 0;
    if (agentConfig.model.pricing) {
      const { inputRate, outputRate } = agentConfig.model.pricing;

      const inputCost = tokenUsage.promptTokens * inputRate;
      const outputCost = tokenUsage.completionTokens * outputRate;
      pointCost = inputCost + outputCost;
      this.usages.push({
        agentId: agentId,
        model: agentConfig.model.name,
        inputTokens: tokenUsage.promptTokens,
        outputTokens: tokenUsage.completionTokens,
        totalTokens: tokenUsage.totalTokens,
        pointCost: pointCost,
      });
      logger.debug(`Calculated point cost for agent ${agentId}: ${pointCost}`);
    } else {
      logger.warn(
        `No pricing information found for agent: ${agentId}. Cost will be 0.`,
      );
    }

    // Simplified: Focus only on token usage and balance updates
    // Message saving is now handled in the main streaming loop (on_chat_model_end)

    logger.debug(`Processing token usage for LLM completion`, {
      userId: this.userId,
      threadId: this.threadId,
      runId: this.runId,
      agentId,
      pointCost,
      tokenUsage: {
        promptTokens: tokenUsage.promptTokens,
        completionTokens: tokenUsage.completionTokens,
        totalTokens: tokenUsage.totalTokens,
      },
    });

    // Real-time token balance update and event emission
    // Note: No completion detection needed - handleLLMEnd only fires for completed responses
    try {
      // Update user's token balance immediately
      const updatedBalance =
        await this.userUsageQueries.updatePointBalanceByUserId(
          pointCost,
          this.userId,
        );

      logger.debug(
        `Updated token balance for user ${this.userId}: cost=${pointCost}, newBalance=${updatedBalance}`,
        {
          userId: this.userId,
          threadId: this.threadId,
          runId: this.runId,
          agentId,
          pointCost,
          updatedBalance,
        },
      );

      // Emit update_rpoint event for real-time frontend updates
      logger.debug(`🚀 About to emit update_rpoint event for user ${this.userId}`, {
        threadId: this.threadId,
        runId: this.runId,
        agentId,
        pointCost,
        updatedBalance,
        hasEmitCallback: !!this.emitEventCallback,
      });

      await this.emitEventCallback({
        type: 'update_rpoint',
        data: {
          rPointCost: pointCost,
          updatedBalance,
          timestamp: Date.now(),
        },
      });

      logger.debug(`✅ Successfully emitted update_rpoint event for user ${this.userId}`, {
        threadId: this.threadId,
        runId: this.runId,
        pointCost,
        updatedBalance,
      });
    } catch (error) {
      logger.error(
        `Failed to update token balance or emit event for user ${this.userId}:`,
        {
          userId: this.userId,
          threadId: this.threadId,
          runId: this.runId,
          agentId,
          pointCost,
          error: error.message,
          stack: error.stack,
        },
      );
    }
  }
}
