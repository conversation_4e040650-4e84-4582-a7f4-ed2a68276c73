import type { CompletionResult } from '../schemas/agent-system.schema';
import { EmitEventCallback } from './event';

/**
 * Context for thread completion handling
 */
export interface CompletionContext {
  /** Thread ID */
  threadId: string;
  /** Run data from database */
  runData: any;
  /** Accumulated partial tokens */
  partialTokens: string[];
  /** Abort controller for cancellation */
  abortController: AbortController;
  /** Optional callback for event emission */
  emitEventCallback?: EmitEventCallback;
  checkpointId?: string;
}

/**
 * Configuration for thread completion handling
 */
export interface CompletionConfig {
  /** Thread ID */
  threadId: string;
  /** Run data */
  runData: any;
  /** Partial tokens accumulated */
  partialTokens: string[];
  /** Whether processing was successful */
  success: boolean;
  /** Error if processing failed */
  error?: Error;
  /** Redis producer client */
  producer: any;
}

/**
 * Configuration for cleanup operations
 */
export interface CleanupConfig {
  /** Thread ID to cleanup */
  threadId: string;
  /** Run ID */
  runId: string;
  /** Redis producer client */
  producer?: any;
  /** Whether cleanup was due to error */
  isError?: boolean;
  /** Error that caused cleanup */
  error?: Error;
}

/**
 * Service interface for thread completion handling
 */
export interface ThreadCompletionHandler {
  /**
   * Handle successful completion
   * @param context - Completion context
   * @returns Completion result
   */
  handleSuccessfulCompletion(context: CompletionContext): Promise<CompletionResult>;

  /**
   * Handle error completion
   * @param context - Completion context
   * @param error - Error that occurred
   * @returns Completion result
   */
  handleErrorCompletion(context: CompletionContext, error: Error): Promise<CompletionResult>;

  /**
   * Handle final cleanup
   * @param context - Completion context
   * @returns Completion result
   */
  handleFinalCleanup(context: CompletionContext): Promise<CompletionResult>;
}

/**
 * Service interface for thread completion handling
 */
export interface ThreadCompletionService {
  /**
   * Handle successful completion
   * @param config - Completion configuration
   */
  handleSuccess(config: CompletionConfig): Promise<void>;
  
  /**
   * Handle error completion
   * @param config - Completion configuration
   */
  handleError(config: CompletionConfig): Promise<void>;
  
  /**
   * Perform cleanup operations
   * @param config - Cleanup configuration
   */
  cleanup(config: CleanupConfig): Promise<void>;
}
