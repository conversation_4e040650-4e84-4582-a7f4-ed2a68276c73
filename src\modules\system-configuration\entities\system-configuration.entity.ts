import { Column, Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng system_configuration trong cơ sở dữ liệu
 * Lưu trữ thông tin cấu hình của hệ thống
 */
@Entity('system_configuration')
export class SystemConfiguration {
  /**
   * ID của cấu hình
   */
  @PrimaryColumn({ type: 'integer' })
  id: number;

  /**
   * Mã ngân hàng
   */
  @Column({ name: 'bank_code', length: 20, nullable: true })
  bankCode: string;

  /**
   * Số tài khoản
   */
  @Column({ name: 'account_number', length: 255, nullable: true })
  accountNumber: string;

  /**
   * Tên tài khoản
   */
  @Column({ name: 'account_name', length: 255, nullable: true })
  accountName: string;

  /**
   * Kích hoạt hay không
   */
  @Column({ name: 'active', type: 'boolean', default: true })
  active: boolean;

  /**
   * Phần trăm phí sàn
   * Ví dụ: 2.50 nghĩa là 2.50%
   */
  @Column({ name: 'fee_percentage', type: 'double precision', nullable: true })
  feePercentage: number;

  /**
   * Link mẫu hóa đơn đầu vào
   */
  @Column({ name: 'purchase_invoice_template', length: 255, nullable: true })
  purchaseInvoiceTemplate: string;

  /**
   * ID của tài khoản email notification
   */
  @Column({
    name: 'email_notification_system_id',
    type: 'integer',
    nullable: true,
  })
  emailNotificationSystemId: number;

  /**
   * Hợp đồng đối tác kinh doanh ban đầu
   */
  @Column({
    name: 'initial_affiliate_contract_business',
    length: 255,
    nullable: true,
  })
  initialAffiliateContractBusiness: string;

  /**
   * Quy tắc hợp đồng kinh doanh ban đầu
   */
  @Column({
    name: 'initial_rule_contract_business',
    length: 255,
    nullable: true,
  })
  initialRuleContractBusiness: string;

  /**
   * Hợp đồng đối tác khách hàng ban đầu
   */
  @Column({
    name: 'initial_affiliate_contract_customer',
    length: 255,
    nullable: true,
  })
  initialAffiliateContractCustomer: string;

  /**
   * Quy tắc hợp đồng khách hàng ban đầu
   */
  @Column({
    name: 'initial_rule_contract_customer',
    length: 255,
    nullable: true,
  })
  initialRuleContractCustomer: string;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number;
}
