import { <PERSON>, Get, Param, Req, Re<PERSON>, Logger } from '@nestjs/common';
import { Request, Response } from 'express';
import { EmailTrackingService } from './services';

/**
 * Controller xử lý tracking email
 */
@Controller('api/email-tracking')
export class EmailTrackingController {
  private readonly logger = new Logger(EmailTrackingController.name);

  constructor(private readonly emailTrackingService: EmailTrackingService) {}

  /**
   * Endpoint cho tracking pixel
   * @param trackingId ID tracking từ URL
   * @param req Request object
   * @param res Response object
   */
  @Get('pixel/:trackingId')
  async trackPixel(
    @Param('trackingId') trackingId: string,
    @Req() req: Request,
    @Res() res: Response,
  ): Promise<void> {
    try {
      // Lấy thông tin metadata từ request
      const metadata = {
        ip: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent'),
        referer: req.get('Referer'),
        timestamp: Date.now(),
      };

      // Track email opened
      await this.emailTrackingService.trackEmailOpened(trackingId, metadata);

      this.logger.debug(`Email opened tracked: ${trackingId}`);

      // Trả về ảnh pixel 1x1 transparent
      const pixelBuffer = Buffer.from(
        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        'base64',
      );

      res.set({
        'Content-Type': 'image/png',
        'Content-Length': pixelBuffer.length.toString(),
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      });

      res.send(pixelBuffer);
    } catch (error) {
      this.logger.error(`Error tracking pixel: ${error.message}`, error.stack);

      // Vẫn trả về pixel để không làm hỏng email
      const pixelBuffer = Buffer.from(
        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        'base64',
      );

      res.set({
        'Content-Type': 'image/png',
        'Content-Length': pixelBuffer.length.toString(),
      });

      res.send(pixelBuffer);
    }
  }

  /**
   * Endpoint để track click links (optional)
   * @param trackingId ID tracking
   * @param url URL gốc cần redirect
   * @param req Request object
   * @param res Response object
   */
  @Get('click/:trackingId')
  async trackClick(
    @Param('trackingId') trackingId: string,
    @Req() req: Request,
    @Res() res: Response,
  ): Promise<void> {
    try {
      const url = req.query.url as string;

      if (!url) {
        res.status(400).send('Missing URL parameter');
        return;
      }

      // Lấy thông tin metadata từ request
      const metadata = {
        ip: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent'),
        referer: req.get('Referer'),
        clickedUrl: url,
        timestamp: Date.now(),
      };

      // Track email clicked (có thể extend EmailTrackingService để support click tracking)
      // await this.emailTrackingService.trackEmailClicked(trackingId, metadata);

      this.logger.debug(`Email click tracked: ${trackingId} -> ${url}`);

      // Redirect đến URL gốc
      res.redirect(url);
    } catch (error) {
      this.logger.error(`Error tracking click: ${error.message}`, error.stack);

      // Redirect về trang chủ nếu có lỗi
      const fallbackUrl = (req.query.url as string) || 'https://redai.com';
      res.redirect(fallbackUrl);
    }
  }
}
