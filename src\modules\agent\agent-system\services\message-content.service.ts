import { Injectable, Logger } from '@nestjs/common';
import { UserMessagesQueries, MessageContent } from './user-messages.queries';
import {
  ContentBlock,
  isTextContentBlock,
  isImageContentBlock,
  isFileContentBlock,
  textContentBlockToXml,
  imageContentBlockToXml,
  fileContentBlockToXml,
} from '../interfaces/message.interface';

/**
 * Service for processing message content and extracting information
 * Handles business logic for message content, separate from DB queries
 */
@Injectable()
export class MessageContentService {
  private readonly logger = new Logger(MessageContentService.name);

  constructor(private readonly userMessagesQueries: UserMessagesQueries) {}

  /**
   * Extract readable text content from message content
   * @param messageContent Message content to extract text from
   * @returns Readable text string
   */
  extractTextFromMessageContent(messageContent: MessageContent): string {
    try {
      // If it's a user message with content blocks
      if (!messageContent?.contentBlocks?.length) {
        return '[Message content unavailable]';
      }
      const textParts: string[] = [];
      for (const block of messageContent.contentBlocks) {
        if (isTextContentBlock(block)) {
          textParts.push(textContentBlockToXml(block));
        } else if (isImageContentBlock(block)) {
          textParts.push(imageContentBlockToXml(block));
        } else if (isFileContentBlock(block)) {
          textParts.push(fileContentBlockToXml(block));
        }
      }
      return textParts.join('\n').trim();
    } catch (error) {
      this.logger.warn(`Failed to extract text from message content:`, error);
      return '[Error extracting message content]';
    }
  }

  /**
   * Get message text content by ID (for reply context)
   * @param messageId Message ID to get text content for
   * @returns Promise<string | null> Text content or null if not found
   */
  async getMessageTextById(messageId: string): Promise<string | null> {
    try {
      const message = await this.userMessagesQueries.getMessageById(messageId);

      if (!message) {
        return null;
      }

      return this.extractTextFromMessageContent(message.content);
    } catch (error) {
      this.logger.error(`Failed to get message text for ${messageId}:`, error);
      return null;
    }
  }
}
