import type {
  ThreadConfiguration,
  StreamingComponents,
  EventProcessingContext,
  ProcessingResult
} from '../schemas/agent-system.schema';
import type { EmitEventCallback } from './event';

/**
 * Service interface for thread configuration
 */
export interface ThreadConfigurationService {
  /**
   * Build thread configuration from run data
   * @param runData - Run data from database
   * @param threadId - Thread ID
   * @param jwt - JWT token
   * @returns Thread configuration
   */
  buildConfiguration(runData: any, threadId: string, jwt: string): Promise<ThreadConfiguration>;
}

/**
 * Service interface for streaming setup
 */
export interface StreamingSetupService {
  /**
   * Setup streaming components with everything needed from start
   * @param config - Thread configuration
   * @param emitEventCallback - Real emit event callback from AgentSystemService
   * @param abortController - Abort controller for cancellation support
   * @returns Streaming components
   */
  setupComponents(config: ThreadConfiguration, emitEventCallback: EmitEventCallback, abortController: AbortController): Promise<StreamingComponents>;
}

/**
 * Service interface for event processing
 */
export interface EventProcessorService {
  /**
   * Process streaming events
   * @param streamingIterator - Iterator for streaming events
   * @param baseContext - Base context for event processing
   * @returns Processing result
   */
  processEvents(
    streamingIterator: AsyncIterableIterator<any>,
    baseContext: Omit<EventProcessingContext, 'event' | 'data' | 'tags'>
  ): Promise<ProcessingResult>;
}
