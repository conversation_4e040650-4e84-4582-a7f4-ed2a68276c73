import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import * as nodemailer from 'nodemailer';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { QueueName } from '../../../queue';
import { EmailMarketingJobDto, BatchEmailMarketingJobDto, EmailMarketingJobName } from './dto';
import { EmailTemplateService, EmailTrackingService, EmailMarketingService } from './services';
import { UserAudienceCustomField } from '../entities/user-audience-custom-field.entity';
import { env } from '../../../config/env';

/**
 * Processor xử lý queue email marketing
 */
@Injectable()
@Processor(QueueName.EMAIL_MARKETING, { concurrency: 10 })
export class EmailMarketingProcessor extends WorkerHost {
  private readonly logger = new Logger(EmailMarketingProcessor.name);

  constructor(
    private readonly emailTemplateService: EmailTemplateService,
    private readonly emailTrackingService: EmailTrackingService,
    private readonly emailMarketingService: EmailMarketingService,
    @InjectRepository(UserAudienceCustomField)
    private readonly customFieldRepository: Repository<UserAudienceCustomField>,
  ) {
    super();
  }

  /**
   * Xử lý job gửi email marketing
   * @param job Job từ queue
   */
  async process(job: Job<EmailMarketingJobDto | BatchEmailMarketingJobDto>): Promise<void> {
    // Xác định loại job dựa trên job name
    if (job.name === EmailMarketingJobName.SEND_EMAIL) {
      await this.processSingleEmail(job as Job<EmailMarketingJobDto>);
    } else if (job.name === EmailMarketingJobName.SEND_BATCH_EMAIL) {
      await this.processBatchEmail(job as Job<BatchEmailMarketingJobDto>);
    } else {
      throw new Error(`Unknown job name: ${job.name}`);
    }
  }

  /**
   * Xử lý job gửi email đơn lẻ
   * @param job Job từ queue
   */
  private async processSingleEmail(job: Job<EmailMarketingJobDto>): Promise<void> {
    const jobData = job.data;

    this.logger.log(
      `Processing single email job: ${job.id} for campaign ${jobData.campaignId}`,
    );

    try {
      // Validate job data
      if (!this.validateJobData(jobData)) {
        throw new Error('Invalid job data');
      }

      // Inject custom variables vào subject và content
      const processedSubject = this.emailTemplateService.injectVariables(
        jobData.subject,
        jobData.customFields,
      );

      const processedContent = this.emailTemplateService.injectVariables(
        jobData.content,
        jobData.customFields,
      );

      // Inject tracking pixel vào content
      const contentWithTracking = this.emailTemplateService.injectTrackingPixel(
        processedContent,
        jobData.trackingId,
        env.app.BASE_URL || 'http://localhost:3000',
      );

      // Tạo email transporter (server config đã được validate)
      const transporter = this.createTransporter(jobData.server!);

      // Chuẩn bị email options
      const mailOptions = {
        from: jobData.server!.from || `no-reply@${jobData.server!.host}`,
        to: jobData.email,
        subject: processedSubject,
        html: contentWithTracking,
      };

      // Gửi email
      const info = await transporter.sendMail(mailOptions);

      // Track email sent
      await this.emailTrackingService.trackEmailSent(
        jobData.campaignId,
        jobData.audienceId,
        jobData.email,
        jobData.trackingId,
      );

      this.logger.log(
        `Email sent successfully: ${info.messageId} to ${jobData.email}`,
      );

      // Update job progress
      await job.updateProgress(100);
    } catch (error) {
      this.logger.error(
        `Error processing email job ${job.id}: ${error.message}`,
        error.stack,
      );

      // Track email failed
      await this.emailTrackingService.trackEmailFailed(
        jobData.campaignId,
        jobData.audienceId,
        jobData.email,
        jobData.trackingId,
        error,
      );

      throw error; // Re-throw để Bull có thể retry
    }
  }

  /**
   * Xử lý job gửi batch email
   * @param job Job từ queue
   */
  private async processBatchEmail(job: Job<BatchEmailMarketingJobDto>): Promise<void> {
    const jobData = job.data;

    this.logger.log(
      `Processing batch email job: ${job.id} for campaign ${jobData.campaignId} with ${jobData.recipients.length} recipients`,
    );

    try {
      // Validate batch job data
      if (!this.validateBatchJobData(jobData)) {
        throw new Error('Invalid batch job data');
      }

      // Lấy template từ database
      const template = await this.emailMarketingService.getTemplateById(jobData.templateId);
      if (!template) {
        throw new Error(`Template not found: ${jobData.templateId}`);
      }

      let successCount = 0;
      let failedCount = 0;

      // Xử lý từng recipient
      for (let i = 0; i < jobData.recipients.length; i++) {
        const recipient = jobData.recipients[i];

        try {
          // Lấy custom fields cho audience này
          const customFields = await this.getAudienceCustomFields(recipient.audienceId);

          // Kết hợp template variables với custom fields của audience
          const combinedVariables = {
            ...jobData.templateVariables,
            ...customFields,
          };

          // Inject variables vào subject và content
          const processedSubject = this.emailTemplateService.injectVariables(
            template.subject || '',
            combinedVariables,
          );

          const processedContent = this.emailTemplateService.injectVariables(
            template.content || '',
            combinedVariables,
          );

          // Tạo tracking ID
          const trackingId = this.emailTrackingService.generateTrackingId(
            jobData.campaignId,
            recipient.audienceId,
          );

          // Inject tracking pixel vào content
          const contentWithTracking = this.emailTemplateService.injectTrackingPixel(
            processedContent,
            trackingId,
            env.app.BASE_URL || 'http://localhost:3000',
          );

          // Tạo email transporter - sử dụng server config từ job hoặc fallback
          const serverConfig = this.getValidServerConfig(jobData.server);
          const transporter = this.createTransporter(serverConfig);

          // Chuẩn bị email options
          const mailOptions = {
            from: serverConfig.from || `no-reply@${serverConfig.host}`,
            to: recipient.email,
            subject: processedSubject,
            html: contentWithTracking,
          };

          // Gửi email
          const info = await transporter.sendMail(mailOptions);

          // Track email sent
          await this.emailTrackingService.trackEmailSent(
            jobData.campaignId,
            recipient.audienceId,
            recipient.email,
            trackingId,
          );

          successCount++;
          this.logger.debug(
            `Email sent successfully: ${info.messageId} to ${recipient.email}`,
          );

        } catch (error) {
          failedCount++;
          this.logger.error(
            `Error sending email to ${recipient.email}: ${error.message}`,
          );

          // Track email failed
          const trackingId = this.emailTrackingService.generateTrackingId(
            jobData.campaignId,
            recipient.audienceId,
          );

          await this.emailTrackingService.trackEmailFailed(
            jobData.campaignId,
            recipient.audienceId,
            recipient.email,
            trackingId,
            error,
          );
        }

        // Update job progress
        const progress = Math.round(((i + 1) / jobData.recipients.length) * 100);
        await job.updateProgress(progress);
      }

      this.logger.log(
        `Batch email job ${job.id} completed: ${successCount} sent, ${failedCount} failed`,
      );

    } catch (error) {
      this.logger.error(
        `Error processing batch email job ${job.id}: ${error.message}`,
        error.stack,
      );
      throw error; // Re-throw để Bull có thể retry
    }
  }

  /**
   * Validate job data
   * @param jobData Dữ liệu job
   * @returns True nếu valid
   */
  private validateJobData(jobData: EmailMarketingJobDto): boolean {
    if (!jobData.campaignId || !jobData.audienceId) {
      this.logger.error('Missing campaignId or audienceId');
      return false;
    }

    if (!jobData.email || !this.isValidEmail(jobData.email)) {
      this.logger.error(`Invalid email: ${jobData.email}`);
      return false;
    }

    if (!jobData.trackingId) {
      this.logger.error('Missing trackingId');
      return false;
    }

    // Validate server config - BẮT BUỘC cho email marketing
    if (!jobData.server) {
      this.logger.error('Missing server configuration for email marketing');
      return false;
    }

    if (!jobData.server.host || !jobData.server.user || !jobData.server.password) {
      this.logger.error('Incomplete server configuration: missing host, user, or password');
      return false;
    }

    return true;
  }

  /**
   * Validate batch job data
   * @param jobData Dữ liệu batch job
   * @returns True nếu valid
   */
  private validateBatchJobData(jobData: BatchEmailMarketingJobDto): boolean {
    if (!jobData.campaignId) {
      this.logger.error('Missing campaignId');
      return false;
    }

    if (!jobData.templateId) {
      this.logger.error('Missing templateId');
      return false;
    }

    if (!jobData.recipients || !Array.isArray(jobData.recipients) || jobData.recipients.length === 0) {
      this.logger.error('Missing or empty recipients array');
      return false;
    }

    // Validate từng recipient
    for (const recipient of jobData.recipients) {
      if (!recipient.audienceId) {
        this.logger.error(`Missing audienceId for recipient: ${recipient.email}`);
        return false;
      }

      if (!recipient.email || !this.isValidEmail(recipient.email)) {
        this.logger.error(`Invalid email: ${recipient.email}`);
        return false;
      }
    }

    return true;
  }

  /**
   * Lấy cấu hình server hợp lệ (kết hợp server config từ job với default)
   * @param serverConfig Server config từ job (có thể undefined hoặc thiếu fields)
   * @returns Cấu hình server đầy đủ
   */
  private getValidServerConfig(serverConfig?: BatchEmailMarketingJobDto['server']): NonNullable<EmailMarketingJobDto['server']> {
    const defaultConfig = this.getDefaultServerConfig();

    if (!serverConfig) {
      return defaultConfig;
    }

    // Merge server config từ job với default config
    return {
      host: serverConfig.host || defaultConfig.host,
      port: serverConfig.port || defaultConfig.port,
      secure: serverConfig.secure ?? defaultConfig.secure,
      user: serverConfig.user || defaultConfig.user,
      password: serverConfig.password || defaultConfig.password,
      from: serverConfig.from || defaultConfig.from,
    };
  }

  /**
   * Lấy cấu hình server mặc định (fallback)
   * @returns Cấu hình server mặc định
   */
  private getDefaultServerConfig(): NonNullable<EmailMarketingJobDto['server']> {
    // Trả về cấu hình mặc định từ env nếu không có server config
    return {
      host: env.email.MAIL_HOST,
      port: env.email.MAIL_PORT,
      secure: env.email.MAIL_SECURE,
      user: env.email.MAIL_USERNAME,
      password: env.email.MAIL_PASSWORD,
      from: env.email.MAIL_DEFAULT_FROM,
    };
  }

  /**
   * Lấy custom fields cho một audience
   * @param audienceId ID của audience
   * @returns Object chứa custom fields
   */
  private async getAudienceCustomFields(
    audienceId: number,
  ): Promise<Record<string, any>> {
    try {
      const customFields = await this.customFieldRepository.find({
        where: { audienceId },
      });

      const result: Record<string, any> = {};

      for (const field of customFields) {
        if (field.fieldName) {
          result[field.fieldName] = field.fieldValue;
        }
      }

      return result;
    } catch (error) {
      this.logger.error(
        `Error getting custom fields for audience ${audienceId}: ${error.message}`,
      );
      return {};
    }
  }

  /**
   * Validate email format
   * @param email Email cần validate
   * @returns True nếu email hợp lệ
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Tạo email transporter
   * @param serverConfig Cấu hình server (BẮT BUỘC)
   * @returns Nodemailer transporter
   */
  private createTransporter(serverConfig: NonNullable<EmailMarketingJobDto['server']>): nodemailer.Transporter {
    // Chỉ sử dụng cấu hình từ server config - KHÔNG fallback về env
    const config = {
      host: serverConfig.host,
      port: serverConfig.port || 587, // Default SMTP port
      secure: serverConfig.secure ?? false, // Default không secure
      auth: {
        user: serverConfig.user,
        pass: serverConfig.password,
      },
    };

    return nodemailer.createTransport(config);
  }

  /**
   * Xử lý khi job failed
   * @param job Job bị failed
   * @param err Lỗi
   */
  async onFailed(job: Job<EmailMarketingJobDto | BatchEmailMarketingJobDto>, err: Error): Promise<void> {
    this.logger.error(`Job ${job.id} failed: ${err.message}`, err.stack);

    // Xử lý khác nhau cho từng loại job
    if (job.name === EmailMarketingJobName.SEND_EMAIL) {
      const jobData = job.data as EmailMarketingJobDto;

      // Track email failed nếu chưa track
      try {
        await this.emailTrackingService.trackEmailFailed(
          jobData.campaignId,
          jobData.audienceId,
          jobData.email,
          jobData.trackingId,
          err,
        );
      } catch (trackingError) {
        this.logger.error(
          `Error tracking failed email: ${trackingError.message}`,
        );
      }
    } else if (job.name === EmailMarketingJobName.SEND_BATCH_EMAIL) {
      const jobData = job.data as BatchEmailMarketingJobDto;
      this.logger.error(`Batch email job ${job.id} failed for campaign ${jobData.campaignId}`);
      // Batch job failure được xử lý trong processBatchEmail method
    }
  }

  /**
   * Xử lý khi job completed
   * @param job Job đã hoàn thành
   */
  async onCompleted(job: Job<EmailMarketingJobDto | BatchEmailMarketingJobDto>): Promise<void> {
    if (job.name === EmailMarketingJobName.SEND_EMAIL) {
      const jobData = job.data as EmailMarketingJobDto;
      this.logger.debug(`Single email job ${job.id} completed for campaign ${jobData.campaignId}`);
    } else if (job.name === EmailMarketingJobName.SEND_BATCH_EMAIL) {
      const jobData = job.data as BatchEmailMarketingJobDto;
      this.logger.debug(`Batch email job ${job.id} completed for campaign ${jobData.campaignId}`);
    }
  }

  /**
   * Xử lý khi job active
   * @param job Job đang xử lý
   */
  async onActive(job: Job<EmailMarketingJobDto | BatchEmailMarketingJobDto>): Promise<void> {
    if (job.name === EmailMarketingJobName.SEND_EMAIL) {
      const jobData = job.data as EmailMarketingJobDto;
      this.logger.debug(`Single email job ${job.id} started for campaign ${jobData.campaignId}`);
    } else if (job.name === EmailMarketingJobName.SEND_BATCH_EMAIL) {
      const jobData = job.data as BatchEmailMarketingJobDto;
      this.logger.debug(`Batch email job ${job.id} started for campaign ${jobData.campaignId}`);
    }
  }
}
