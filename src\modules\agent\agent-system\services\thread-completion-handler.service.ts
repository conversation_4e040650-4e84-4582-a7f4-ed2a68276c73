import { Injectable, Logger } from '@nestjs/common';
import {
  CompletionContext,
  ThreadCompletionHandler as IThreadCompletionHandler,
} from '../interfaces/completion.interface';
import { CompletionResult } from '../schemas';
import { EmitEventCallback } from '../interfaces/event';
import { UserMessagesQueries } from './user-messages.queries';
import { UserAgentRunsQueries } from './user-agent-runs.queries';
import { UserAgentRunStatus } from '../../enums';
import { RedisService } from '../../../../infra';
import { workflow } from '../core';
import { AIMessage } from '@langchain/core/messages';

/**
 * Service responsible for handling thread completion, cleanup, and finalization
 * Extracted from AgentSystemService.processAgentThread method (Section 5: Success Completion, Section 6: Error Handling, Section 7: Finalization)
 */
@Injectable()
export class ThreadCompletionHandlerService
  implements IThreadCompletionHandler
{
  private readonly logger = new Logger(ThreadCompletionHandlerService.name);

  constructor(
    private readonly userMessagesQueries: UserMessagesQueries,
    private readonly userAgentRunsQueries: UserAgentRunsQueries,
    private readonly redisService: RedisService,
  ) {}

  /**
   * Handle successful completion of thread processing, also save response to database
   * Extracted from processAgentThread lines 684-716
   * @param context - Completion context with thread data
   * @returns Completion result
   */
  async handleSuccessfulCompletion(
    context: CompletionContext,
  ): Promise<CompletionResult> {
    try {
      // Log complete response summary
      const completeResponse = context.partialTokens.join('');
      const responsePreview =
        completeResponse.length > 200
          ? `${completeResponse.substring(0, 200)}...`
          : completeResponse;
      const safePreview = responsePreview
        .replace(/\n/g, '\\n')
        .replace(/\r/g, '\\r');

      this.logger.log(
        `✅ LangGraph thread ${context.threadId} completed: "${safePreview}"`,
        {
          threadId: context.threadId,
          runId: context.runData.id,
          totalTokens: context.partialTokens.length,
          totalChars: completeResponse.length,
          responsePreview: safePreview,
        },
      );

      // ✅ FIX: Update database status to COMPLETED
      try {
        await this.userAgentRunsQueries.updateRunStatus(
          context.runData.id,
          UserAgentRunStatus.COMPLETED,
        );
        this.logger.debug(
          `✅ Updated run ${context.runData.id} status to COMPLETED`,
        );
      } catch (statusError) {
        this.logger.error(`❌ Failed to update run status to COMPLETED:`, {
          runId: context.runData.id,
          threadId: context.threadId,
          error: statusError.message,
        });
      }

      // Cleanup thread resources
      await this.cleanupThreadResources(context);

      return {
        success: true,
        threadId: context.threadId,
        runId: context.runData.id,
        totalTokens: context.partialTokens.length,
        totalChars: completeResponse.length,
      };
    } catch (error) {
      this.logger.error(
        `Error in successful completion handling for thread ${context.threadId}:`,
        {
          threadId: context.threadId,
          runId: context.runData.id,
          error: error.message,
          stack: error.stack,
        },
      );

      return {
        success: false,
        threadId: context.threadId,
        runId: context.runData.id,
        error: error.message,
      };
    }
  }

  /**
   * Handle error completion of thread processing
   * Extracted from processAgentThread lines 717-765
   * @param context - Completion context with thread data
   * @param error - Error that occurred during processing
   * @returns Completion result
   */
  async handleErrorCompletion(
    context: CompletionContext,
    error: Error,
  ): Promise<CompletionResult> {
    try {
      const isAbortError =
        context.abortController.signal.aborted ||
        (error instanceof TypeError &&
          error.message.includes(
            'Invalid state: The reader is not attached to a stream',
          ));

      if (isAbortError) {
        // Swallow it: this is expected on cancel
        this.logger.log('LangGraph stream aborted cleanly.', {
          threadId: context.threadId,
          runId: context.runData.id,
        });

        // ✅ FIX: Update database status to CANCELLED for aborted runs
        try {
          await this.userAgentRunsQueries.updateRunStatus(
            context.runData.id,
            UserAgentRunStatus.CANCELLED,
          );
          this.logger.debug(
            `✅ Updated run ${context.runData.id} status to CANCELLED`,
          );
        } catch (statusError) {
          this.logger.error(`❌ Failed to update run status to CANCELLED:`, {
            runId: context.runData.id,
            threadId: context.threadId,
            error: statusError.message,
          });
        }

        return {
          success: true,
          threadId: context.threadId,
          runId: context.runData.id,
          cancelled: true,
        };
      } else {
        // Unexpected error: log with full stack trace, emit error event, and cleanup
        this.logger.error(
          `Error in LangGraph streaming for ${context.threadId}:`,
          {
            message: error.message,
            stack: error.stack,
            name: error.name,
            threadId: context.threadId,
            runId: context.runData.id,
            error: error,
          },
        );

        // Emit error event to SSE stream so frontend knows about the failure
        if (context.emitEventCallback) {
          try {
            await context.emitEventCallback({
              type: 'stream_error',
              data: {
                error: error.message || 'Unknown streaming error',
                errorName: error.name,
                stack: error.stack,
                threadId: context.threadId,
                runId: context.runData.id,
                timestamp: Date.now(),
              },
            });
          } catch (emitError) {
            this.logger.error(
              `Failed to emit error event for thread ${context.threadId}:`,
              {
                threadId: context.threadId,
                runId: context.runData.id,
                emitError: emitError.message,
              },
            );
          }
        }

        // ✅ FIX: Update database status to FAILED for unexpected errors
        try {
          await this.userAgentRunsQueries.updateRunStatus(
            context.runData.id,
            UserAgentRunStatus.FAILED,
          );
          this.logger.debug(
            `✅ Updated run ${context.runData.id} status to FAILED`,
          );
        } catch (statusError) {
          this.logger.error(`❌ Failed to update run status to FAILED:`, {
            runId: context.runData.id,
            threadId: context.threadId,
            error: statusError.message,
          });
        }

        await this.cleanupThreadResources(context);

        return {
          success: false,
          threadId: context.threadId,
          runId: context.runData.id,
          error: error.message,
        };
      }
    } catch (handlingError) {
      this.logger.error(
        `Error in error completion handling for thread ${context.threadId}:`,
        {
          threadId: context.threadId,
          runId: context.runData.id,
          originalError: error.message,
          handlingError: handlingError.message,
        },
      );

      return {
        success: false,
        threadId: context.threadId,
        runId: context.runData.id,
        error: `${error.message} (handling error: ${handlingError.message})`,
      };
    }
  }

  /**
   * Handle final cleanup and session end
   * Extracted from processAgentThread finally block lines 766-821
   * Now handles ALL cases: success, error, and cancellation
   * @param context - Completion context with thread data
   * @param error - Optional error that occurred during processing
   * @returns Completion result
   */
  async handleFinalCleanup(
    context: CompletionContext,
    error?: Error,
  ): Promise<CompletionResult> {
    try {
      // 🎯 UNIFIED MESSAGE SAVING: Handle ALL cases (success, error, cancellation)
      if (context.partialTokens.length > 0) {
        const partialText = context.partialTokens.join('');
        const displayText =
          partialText.length > 100
            ? `${partialText.substring(0, 100)}...`
            : partialText;
        const safeText = displayText
          .replace(/\n/g, '\\n')
          .replace(/\r/g, '\\r');

        const isCancelled = context.abortController.signal.aborted;
        const hasError = !!error;
        const messageType = isCancelled
          ? 'cancelled'
          : hasError
            ? 'error'
            : 'complete';

        this.logger.log(
          `💾 Saving ${messageType} response for ${context.threadId}: "${safeText}"`,
          {
            tokenCount: context.partialTokens.length,
            textLength: partialText.length,
            preview: safeText,
            cancelled: isCancelled,
            hasError,
          },
        );
        // Save response with complete=true for all cases (we have content)
        const messageId = await this.saveResponse(
          context.threadId,
          context.runData.id,
          partialText,
          context.emitEventCallback,
        );
        if (messageType !== 'complete') {
          await workflow.updateState(
            {
              configurable: {
                thread_id: context.threadId,
              },
            },
            {
              messages: [
                new AIMessage({
                  id: messageId,
                  content: [
                    {
                      type: 'text',
                      text: partialText,
                    },
                  ],
                }),
              ],
            },
          );
        }
      }

      // 👇 FINAL EVENT: Tell the StreamController to close the connection
      // This event's only purpose is to signal the end of the streaming session
      if (context.emitEventCallback) {
        try {
          await context.emitEventCallback({
            type: 'stream_session_end',
            data: { reason: 'Processing complete' },
          });

          this.logger.debug(
            `📡 Emitted stream_session_end event for thread ${context.threadId}`,
            {
              threadId: context.threadId,
              runId: context.runData.id,
              reason: 'Processing complete',
            },
          );
        } catch (sessionEndError) {
          this.logger.error(
            `Failed to emit stream_session_end event for thread ${context.threadId}:`,
            {
              threadId: context.threadId,
              runId: context.runData.id,
              error: sessionEndError.message,
              stack: sessionEndError.stack,
            },
          );
          // Don't throw - this is the final cleanup, shouldn't fail the entire operation
        }
      }

      return {
        success: true,
        threadId: context.threadId,
        runId: context.runData.id,
        finalCleanup: true,
      };
    } catch (error) {
      this.logger.error(
        `Error in final cleanup for thread ${context.threadId}:`,
        {
          threadId: context.threadId,
          runId: context.runData.id,
          error: error.message,
          stack: error.stack,
        },
      );

      return {
        success: false,
        threadId: context.threadId,
        runId: context.runData.id,
        error: error.message,
      };
    }
  }

  /**
   * Create completion context from processing data
   * Helper method to build completion context
   * @param threadId - Thread ID
   * @param runData - Run data from database
   * @param partialTokens - Accumulated partial tokens
   * @param abortController - Abort controller
   * @param emitEventCallback - Event emission callback
   * @param checkpointId
   * @returns Completion context
   */
  createCompletionContext(
    threadId: string,
    runData: any,
    partialTokens: string[],
    abortController: AbortController,
    emitEventCallback?: EmitEventCallback,
    checkpointId?: string,
  ): CompletionContext {
    return {
      threadId,
      runData,
      partialTokens,
      abortController,
      emitEventCallback,
      checkpointId,
    };
  }

  /**
   * Cleanup thread resources
   * Comprehensive cleanup of all resources created during thread processing
   * @param context - Completion context with thread data
   */
  private async cleanupThreadResources(
    context: CompletionContext,
  ): Promise<void> {
    const cleanupTasks: Promise<void>[] = [];
    const threadId = context.threadId;
    const runId = context.runData.id;

    try {
      this.logger.debug(
        `Starting comprehensive cleanup for thread ${threadId} (run ${runId})`,
      );
      await this.cleanupRedisStreams(threadId, runId);
    } catch (error) {
      this.logger.error(
        `Error during comprehensive cleanup for thread ${threadId}:`,
        {
          threadId,
          runId,
          error: error.message,
          stack: error.stack,
        },
      );
      throw error; // Re-throw to indicate cleanup failure
    }
  }

  /**
   * Clean up Redis streams created for this thread
   * @param threadId Thread ID
   * @param runId Run ID
   */
  private async cleanupRedisStreams(
    threadId: string,
    runId: string,
  ): Promise<void> {
    try {
      const streamKey = `agent_stream:${threadId}:${runId}`;
      const redis = this.redisService.getRawClient();

      // Get stream info to check if it exists
      try {
        const streamInfo = await redis.xinfo('STREAM', streamKey);
        if (streamInfo) {
          // Set TTL on the stream instead of deleting immediately
          // This allows backend to read final events before cleanup
          await redis.expire(streamKey, 300); // 5 minutes TTL
          this.logger.debug(`Set TTL on Redis stream: ${streamKey}`);
        }
      } catch (streamError) {
        // Stream doesn't exist or already cleaned up
        this.logger.debug(
          `Redis stream ${streamKey} doesn't exist or already cleaned up`,
        );
      }
    } catch (error) {
      this.logger.warn(
        `Failed to cleanup Redis streams for thread ${threadId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Save response message for any case (complete, cancelled, or error)
   * Unified method to handle all message saving scenarios
   * @param threadId - Thread ID
   * @param runId - Run ID
   * @param responseText - Response text content
   * @param emitEventCallback - Optional callback for event emission
   * @returns Message ID of saved message
   */
  private async saveResponse(
    threadId: string,
    runId: string,
    responseText: string,
    emitEventCallback?: EmitEventCallback,
  ): Promise<string> {
    try {
      // Get userId from the run data
      const runData = await this.userAgentRunsQueries.getRunById(runId);
      if (!runData) {
        this.logger.warn(
          `No run data found for runId ${runId}, cannot save partial response`,
          {
            threadId,
            runId,
          },
        );
        return '';
      }

      const userId = runData.created_by;

      this.logger.log(`💾 Saving assistant response for thread ${threadId}`, {
        threadId,
        responseLength: responseText.length,
        userId,
        preview:
          responseText.substring(0, 100) +
          (responseText.length > 100 ? '...' : ''),
      });

      // Save response WITHOUT token usage data (no token counts, no balance updates)
      const messageId = await this.userMessagesQueries.createMessage({
        thread_id: threadId,
        role: 'assistant',
        content: {
          contentBlocks: [
            {
              type: 'text',
              content: responseText,
            },
          ],
        },
        created_by: userId,
      });

      this.logger.log(
        `✅ Successfully saved assistant response for thread ${threadId}`,
        {
          threadId,
          messageId,
          responseLength: responseText.length,
          userId,
          hasTokenData: false,
        },
      );

      // Emit message_created event for responses (but NO update_rpoint event)
      if (emitEventCallback) {
        try {
          await emitEventCallback({
            type: 'message_created',
            data: {
              message_id: messageId,
              thread_id: threadId,
              role: 'assistant',
              content_preview: responseText.substring(0, 100),
            },
          });

          this.logger.debug(`📡 Emitted message_created event for response`, {
            threadId,
            runId,
            messageId,
          });
        } catch (eventError) {
          this.logger.error(
            `Failed to emit message_created event for partial response:`,
            {
              threadId,
              runId,
              messageId,
              error: eventError.message,
            },
          );
          // Don't throw - event emission failure should not disrupt partial saving
        }
      }

      return messageId;
    } catch (error) {
      this.logger.error(
        `Failed to save partial response for thread ${threadId}:`,
        {
          threadId,
          runId,
          error: error.message,
          stack: error.stack,
        },
      );
      // Don't throw - partial response saving should not fail the cancellation
      return '';
    }
  }
}
