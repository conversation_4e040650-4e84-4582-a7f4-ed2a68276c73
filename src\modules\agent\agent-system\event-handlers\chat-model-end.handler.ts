import { Injectable } from '@nestjs/common';
import { BaseLangGraphEventHandler } from './base-event-handler';
import { EventProcessingContext } from '../schemas';
import { ROLE_TAGS } from '../core/constants';

/**
 * Handler for on_chat_model_end events from LangGraph
 * CRITICAL: This handler saves complete messages to the database
 */
@Injectable()
export class ChatModelEndHandler extends BaseLangGraphEventHandler {
  /**
   * Check if this handler can process the given event
   * @param event - LangGraph event type
   * @param data - Event data
   * @param tags - Event tags
   * @returns True if this is an on_chat_model_end event with role tags
   */
  canHandle(event: string, data: any, tags: string[]): boolean {
    return event === 'on_chat_model_end' && 
           tags.some((t: string) => ROLE_TAGS.includes(t));
  }

  /**
   * Process on_chat_model_end event
   * Saves complete LLM response to database and emits completion events
   * @param context - Event processing context
   */
  async handle(context: EventProcessingContext): Promise<void> {
    const role = this.getRoleFromTags(context.tags, ROLE_TAGS);

    this.logEvent('🏁', `LLM completion finished [${role}]`, context, {
      role,
      event: 'on_chat_model_end',
      partialTokensCount: context.partialTokens.length,
    });

    // Log completion detection for debugging
    const isCompleteResponse = context.partialTokens.length > 0;
    const completeResponseText = context.partialTokens.join('');

    if (isCompleteResponse && completeResponseText.trim().length > 0) {
      this.logger.debug(
        `✅ LLM completion detected: ${completeResponseText.length} characters`,
        {
          threadId: context.threadId,
          role,
          textLength: completeResponseText.length,
          textPreview: completeResponseText.substring(0, 100),
          runId: context.runData.id,
        },
      );
      // Note: Message saving is now handled in handleFinalCleanup to prevent duplicates
    } else {
      this.logger.debug(
        `⚠️ LLM completion detected but no content`,
        {
          threadId: context.threadId,
          role,
          partialTokensCount: context.partialTokens.length,
          hasContent: completeResponseText.trim().length > 0,
        },
      );
    }

    // 🚀 EMIT llm_stream_end LAST (after all processing is complete)
    await context.emitEventCallback({
      type: 'llm_stream_end',
      data: { role: role as 'supervisor' | 'worker' | undefined },
    });

    this.logger.debug(`📡 Emitted llm_stream_end event [${role}]`, {
      threadId: context.threadId,
      runId: context.runData.id,
      role,
    });
  }
}
