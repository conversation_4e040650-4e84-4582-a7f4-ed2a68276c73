import { Module } from '@nestjs/common';
import { AgentSystemController } from './agent-system.controller';
import { UserAgentRunsQueries } from './services/user-agent-runs.queries';
import { UserMessagesQueries } from './services/user-messages.queries';
import { MessageContentService } from './services/message-content.service';
import { ChatDatabaseService } from './database.service';
import { AgentSystemService } from './services/agent-system.service';
import { UserUsageQueries } from './services/user-usage.queries';
import {
  ThreadConfigurationService,
  StreamingSetupService,
  LangGraphEventProcessorService,
  ThreadCompletionHandlerService,
  ValidationService,
} from './services';
import { LangGraphEventRegistry } from './event-handlers';
import { HttpModule } from '@nestjs/axios';

@Module({
  imports: [
    HttpModule,
  ],
  controllers: [AgentSystemController],
  providers: [
    // Existing services
    UserAgentRunsQueries,
    UserMessagesQueries,
    MessageContentService,
    ChatDatabaseService,
    AgentSystemService,
    UserUsageQueries,

    // Refactored services
    ThreadConfigurationService,
    StreamingSetupService,
    LangGraphEventProcessorService,
    ThreadCompletionHandlerService,
    ValidationService,

    // Event handling services
    LangGraphEventRegistry,
  ],
})
export class AgentSystemModule {}
