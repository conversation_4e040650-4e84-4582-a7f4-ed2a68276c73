# Test Email Marketing Module

## <PERSON><PERSON> liệu mẫu để test

### 1. Tạo Campaign trong database

```sql
-- Tạo campaign email mẫu
INSERT INTO user_campaigns (
  id, user_id, title, description, platform, content, subject, 
  status, created_at, updated_at, audience_ids
) VALUES (
  1, 1, 'Welcome Email Campaign', 'Chiến dịch chào mừng khách hàng mới', 
  'email', 
  '<h1><PERSON>n chào {{name}}!</h1><p><PERSON>ảm ơn bạn đã đăng ký. Email: {{email}}</p><p>Thành phố: {{profile.city}}</p>',
  'Chào mừng {{name}} đến với RedAI!',
  'active', 1640995200000, 1640995200000, '[1, 2, 3]'
);
```

### 2. Tạo Audiences

```sql
-- Tạo audiences mẫu
INSERT INTO user_audience (id, user_id, email, phone, created_at, updated_at) VALUES
(1, 1, '<EMAIL>', '0123456789', 1640995200000, 1640995200000),
(2, 1, '<EMAIL>', '0123456790', 1640995200000, 1640995200000),
(3, 1, '<EMAIL>', '0123456791', 1640995200000, 1640995200000);
```

### 3. Tạo Custom Fields

```sql
-- Custom fields cho audience 1
INSERT INTO user_audience_custom_fields (audience_id, field_name, field_value, field_type, created_at, updated_at) VALUES
(1, 'name', '"Nguyễn Văn A"', 'string', 1640995200000, 1640995200000),
(1, 'profile', '{"city": "Hà Nội", "age": 25}', 'object', 1640995200000, 1640995200000);

-- Custom fields cho audience 2
INSERT INTO user_audience_custom_fields (audience_id, field_name, field_value, field_type, created_at, updated_at) VALUES
(2, 'name', '"Trần Thị B"', 'string', 1640995200000, 1640995200000),
(2, 'profile', '{"city": "TP.HCM", "age": 30}', 'object', 1640995200000, 1640995200000);

-- Custom fields cho audience 3
INSERT INTO user_audience_custom_fields (audience_id, field_name, field_value, field_type, created_at, updated_at) VALUES
(3, 'name', '"Lê Văn C"', 'string', 1640995200000, 1640995200000),
(3, 'profile', '{"city": "Đà Nẵng", "age": 28}', 'object', 1640995200000, 1640995200000);
```

## Test APIs

### 1. Tạo jobs cho campaign

```bash
curl -X POST http://localhost:3000/api/email-marketing/campaigns/1/jobs \
  -H "Content-Type: application/json"
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Created 3 email marketing jobs",
  "data": {
    "campaignId": 1,
    "jobCount": 3
  }
}
```

### 2. Kiểm tra queue status

```bash
curl http://localhost:3000/api/email-marketing/queue/status
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "waiting": 3,
    "active": 0,
    "completed": 0,
    "failed": 0,
    "delayed": 0
  }
}
```

### 3. Xem Bull Dashboard

Truy cập: `http://localhost:3000/queues`
- Username: `admin`
- Password: `redai@123`

### 4. Test tracking pixel

Sau khi email được gửi, test tracking:
```bash
curl http://localhost:3000/api/email-tracking/pixel/1_1_1640995200000_abc123
```

**Expected:** Trả về ảnh PNG 1x1 pixel

### 5. Kiểm tra Redis tracking data

```bash
redis-cli keys "email_tracking:*"
redis-cli get "email_tracking:1640995200000:0.123456"
```

### 6. Kiểm tra database tracking history

```sql
SELECT * FROM user_campaign_history 
WHERE campaign_id = 1 
ORDER BY created_at DESC;
```

## Template Testing

### Input Template:
```html
<h1>Xin chào {{name}}!</h1>
<p>Email: {{email}}</p>
<p>Thành phố: {{profile.city}}</p>
<p>Tuổi: {{profile.age}}</p>
```

### Custom Fields Data:
```json
{
  "name": "Nguyễn Văn A",
  "email": "<EMAIL>",
  "profile": {
    "city": "Hà Nội",
    "age": 25
  }
}
```

### Expected Output:
```html
<h1>Xin chào Nguyễn Văn A!</h1>
<p>Email: <EMAIL></p>
<p>Thành phố: Hà Nội</p>
<p>Tuổi: 25</p>
<img src="http://localhost:3000/api/email-tracking/pixel/1_1_1640995200000_abc123" width="1" height="1" style="display:none;" alt="" />
```

## Monitoring

### 1. Logs to watch

```bash
# Service logs
tail -f logs/email-marketing-service.log

# Processor logs  
tail -f logs/email-marketing-processor.log

# Tracking logs
tail -f logs/email-tracking-service.log
```

### 2. Redis monitoring

```bash
# Monitor Redis commands
redis-cli monitor

# Check tracking keys
redis-cli keys "email_tracking:*" | wc -l
```

### 3. Database monitoring

```sql
-- Check campaign history
SELECT 
  status, 
  COUNT(*) as count 
FROM user_campaign_history 
WHERE campaign_id = 1 
GROUP BY status;

-- Recent tracking events
SELECT * FROM user_campaign_history 
WHERE campaign_id = 1 
ORDER BY created_at DESC 
LIMIT 10;
```

## Troubleshooting

### Common Issues:

1. **Jobs không được tạo:**
   - Kiểm tra campaign có tồn tại
   - Kiểm tra platform = 'email'
   - Kiểm tra audienceIds có hợp lệ

2. **Email không gửi:**
   - Kiểm tra SMTP config
   - Kiểm tra email format
   - Xem failed jobs trong Bull Dashboard

3. **Tracking không hoạt động:**
   - Kiểm tra BASE_URL environment
   - Kiểm tra Redis connection
   - Xem tracking controller logs

## ⚠️ Server Config Validation (Updated)

### ✅ Valid Campaign với Server Config

```sql
-- Campaign với server config đầy đủ
UPDATE user_campaigns SET
  server = '{
    "host": "smtp.gmail.com",
    "port": 587,
    "secure": false,
    "user": "<EMAIL>",
    "password": "app-password",
    "from": "Marketing Team <<EMAIL>>"
  }'
WHERE id = 1;
```

### ❌ Invalid Campaign (sẽ bị reject)

```sql
-- Campaign không có server config
UPDATE user_campaigns SET server = NULL WHERE id = 1;
-- ❌ Lỗi: "Campaign 1 missing server configuration"

-- Campaign thiếu thông tin server
UPDATE user_campaigns SET
  server = '{"host": "smtp.gmail.com"}'  -- Thiếu user và password
WHERE id = 1;
-- ❌ Lỗi: "Campaign 1 has incomplete server configuration"
```

### Validation Logic

**Service Level:**
- Kiểm tra `campaign.server` không null
- Kiểm tra `host`, `user`, `password` bắt buộc

**Processor Level:**
- Kiểm tra `jobData.server` không null
- Kiểm tra các field bắt buộc
- **KHÔNG** fallback về env config

### Lợi ích

1. **Bảo mật**: Mỗi campaign dùng server riêng
2. **Linh hoạt**: Có thể dùng nhiều email provider
3. **Kiểm soát**: Admin quản lý từng server
4. **Tách biệt**: Không phụ thuộc env config

## Server Config Validation (Updated)

### ✅ Valid Campaign với Server Config

```sql
-- Campaign với server config đầy đủ
UPDATE user_campaigns SET
  server = '{
    "host": "smtp.gmail.com",
    "port": 587,
    "secure": false,
    "user": "<EMAIL>",
    "password": "app-password",
    "from": "Marketing Team <<EMAIL>>"
  }'
WHERE id = 1;
```

### ❌ Invalid Campaign (sẽ bị reject)

```sql
-- Campaign không có server config
UPDATE user_campaigns SET server = NULL WHERE id = 1;
-- ❌ Lỗi: "Campaign 1 missing server configuration"

-- Campaign thiếu thông tin server
UPDATE user_campaigns SET
  server = '{"host": "smtp.gmail.com"}'  -- Thiếu user và password
WHERE id = 1;
-- ❌ Lỗi: "Campaign 1 has incomplete server configuration"
```

### Validation Logic

**Service Level:**
- Kiểm tra `campaign.server` không null
- Kiểm tra `host`, `user`, `password` bắt buộc

**Processor Level:**
- Kiểm tra `jobData.server` không null
- Kiểm tra các field bắt buộc
- Không fallback về env config

### Lợi ích

1. **Bảo mật**: Mỗi campaign dùng server riêng
2. **Linh hoạt**: Có thể dùng nhiều email provider
3. **Kiểm soát**: Admin quản lý từng server
4. **Tách biệt**: Không phụ thuộc env config
