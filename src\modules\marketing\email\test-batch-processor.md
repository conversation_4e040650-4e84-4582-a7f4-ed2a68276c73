# Test Batch Email Marketing Processor

## Tổng quan

Processor mới đã được tạo để hỗ trợ cả single email và batch email marketing jobs với các tính năng:

### 1. Single Email Job (`send-email`)
- Xử lý email đơn lẻ như trước
- Sử dụng subject/content trực tiếp từ job data
- Y<PERSON>u cầu server config bắt buộc

### 2. Batch Email Job (`send-batch-email`)
- Xử lý nhiều email cùng lúc
- Lấy template từ database bằng templateId
- Kết hợp template variables với custom fields của từng audience
- Server config optional (fallback về env config)

## Cấu trúc Job Data

### Single Email Job
```typescript
interface EmailMarketingJobDto {
  campaignId: number;
  audienceId: number;
  email: string;
  subject: string;
  content: string;
  customFields: Record<string, any>;
  server: {
    host: string;
    port?: number;
    secure?: boolean;
    user: string;
    password: string;
    from?: string;
  };
  trackingId: string;
  createdAt: number;
}
```

### Batch Email Job
```typescript
interface BatchEmailMarketingJobDto {
  campaignId: number;
  templateId: number;
  templateVariables: Record<string, any>;
  recipients: EmailRecipientDto[];
  server?: {
    host?: string;
    port?: number;
    secure?: boolean;
    user?: string;
    password?: string;
    from?: string;
  };
  createdAt: number;
}

interface EmailRecipientDto {
  audienceId: number;
  email: string;
}
```

## Cách sử dụng

### 1. Thêm Single Email Job
```typescript
import { EmailMarketingJobName } from './dto';

await emailMarketingQueue.add(EmailMarketingJobName.SEND_EMAIL, {
  campaignId: 1,
  audienceId: 123,
  email: '<EMAIL>',
  subject: 'Hello {{name}}',
  content: '<h1>Welcome {{name}}</h1>',
  customFields: { name: 'John Doe' },
  server: {
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    user: '<EMAIL>',
    password: 'your-password',
    from: '<EMAIL>'
  },
  trackingId: 'track_123',
  createdAt: Date.now()
});
```

### 2. Thêm Batch Email Job
```typescript
await emailMarketingQueue.add(EmailMarketingJobName.SEND_BATCH_EMAIL, {
  campaignId: 1,
  templateId: 5, // ID của template trong database
  templateVariables: {
    companyName: 'Your Company',
    promotionCode: 'SAVE20'
  },
  recipients: [
    { audienceId: 123, email: '<EMAIL>' },
    { audienceId: 124, email: '<EMAIL>' },
    { audienceId: 125, email: '<EMAIL>' }
  ],
  server: { // Optional - sẽ fallback về env config nếu không có
    host: 'smtp.gmail.com',
    user: '<EMAIL>',
    password: 'your-password'
  },
  createdAt: Date.now()
});
```

## Template Variables Logic

Trong batch email, processor sẽ:

1. Lấy template từ database bằng `templateId`
2. Lấy custom fields của từng audience
3. Kết hợp `templateVariables` với custom fields:
   ```typescript
   const combinedVariables = {
     ...jobData.templateVariables, // Global variables
     ...customFields, // Audience-specific fields
   };
   ```
4. Inject variables vào template subject và content

## Error Handling

- Single email: Track failed email với tracking service
- Batch email: Continue processing other emails nếu một email fail
- Progress tracking: Update job progress trong batch processing
- Retry mechanism: Bull sẽ retry failed jobs theo config

## Testing

Để test processor:

1. Tạo template trong database
2. Tạo audiences với custom fields
3. Add batch job vào queue
4. Monitor queue dashboard tại `/queues`

## Dependencies Added

- `UserTemplateEmail` entity được thêm vào module
- `UserAudienceCustomField` repository được inject vào processor
- Method `getTemplateById` được thêm vào `EmailMarketingService`
