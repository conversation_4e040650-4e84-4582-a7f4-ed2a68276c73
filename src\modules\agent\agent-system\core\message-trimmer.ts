import {
  AIMessage,
  BaseMessage,
  HumanMessage,
  RemoveMessage,
  SystemMessage,
  ToolMessage,
  trimMessages,
} from '@langchain/core/messages';
import { Logger } from '@nestjs/common';
import {
  dropLeadingOrphanAsRemovals,
  dropTrailingOrphanBlock,
} from './helpers';

const logger = new Logger('MessageTrimmer');

async function topKMessageTrimmer(
  messages: BaseMessage[],
  threshold: number,
  model?: any,
): Promise<{ messages: BaseMessage[] }> {
  return { messages };
  // if (messages.length <= threshold) {
  //   logger.log(`📭 No trimming needed: ${messages.length} <= ${threshold}`);
  //   return { messages: [] };
  // }
  //
  // const toDelete = messages.slice(0, Math.max(0, messages.length - threshold));
  // const toKeep = messages.slice(-threshold);
  // const removals = toDelete.map(
  //   (msg: BaseMessage) => new RemoveMessage({ id: msg.id as string }),
  // );
  // const removedOrphan = dropLeadingOrphanAsRemovals(toKeep);
  // logger.log(
  //   `🗑️ Deleting ${removals.length + removedOrphan.length} messages, keeping last ${toKeep.length - removedOrphan.length}`,
  // );
  // return { messages: [...removals, ...removedOrphan] };
}

export async function tokenMessageTrimmer(
  messages: BaseMessage[],
  threshold: number,
  model: any,
): Promise<{ messages: BaseMessage[] }> {
  if (!model) {
    logger.warn(
      'No model provided for token trimming, falling back to no trimming',
    );
    return { messages: [] };
  }

  // 1️⃣ Token‐budget trim (no copy needed)
  const trimmed = await trimMessages(messages, {
    maxTokens: threshold,
    strategy: 'last',
    tokenCounter: model,
    startOn: 'human',
  });

  // 2️⃣ Orphan‐drop at the tail
  const cleaned = dropTrailingOrphanBlock(trimmed);
  if (cleaned.length !== trimmed.length) {
    logger.log(
      `🗑️ Dropped ${trimmed.length - cleaned.length} trailing orphan msg(s)`,
    );
  }

  // 3️⃣ ONE PASS over the original to emit RemoveMessage for any msg
  //    that didn’t survive into `cleaned`.
  const keptIds = new Set(cleaned.map((m) => m.id));
  const removals: RemoveMessage[] = [];
  for (const msg of messages) {
    // Skip if it’s in the final kept set
    if (msg.id && !keptIds.has(msg.id)) {
      removals.push(new RemoveMessage({ id: msg.id }));
    }
  }

  logger.log(`📨 Emitting ${removals.length} RemoveMessage cmds`);

  // 4️⃣ Return just the RemoveMessage directives
  return { messages: removals };
}

async function aiMessageTrimmer(
  messages: BaseMessage[],
  threshold: number,
  model: any,
): Promise<{ messages: BaseMessage[] }> {
  if (messages.length <= threshold) {
    logger.log(`📭 No trimming needed: ${messages.length} <= ${threshold}`);
    return { messages: [] }; // Return empty messages array instead of empty object
  }
  // Summarize the *earliest* messages beyond the threshold
  const toSummarize = messages.slice(0, messages.length - threshold);
  const summarizationInput = `${toSummarize
    .map((msg) => {
      if (msg instanceof AIMessage) {
        return `Assistant: ${JSON.stringify(msg?.content)}`;
      } else if (msg instanceof HumanMessage) {
        return `User: ${JSON.stringify(msg?.content)}`;
      } else if (msg instanceof ToolMessage) {
        return `Tool: ${JSON.stringify(msg?.content)}`;
      } else {
        return '';
      }
    })
    .join('\n')}`;
  const summaryPrompt = [
    new SystemMessage({
      content: `You are an AI assistant that summarizes conversations. You are picky, choosing and synthesizing the most important points from the conversation.`,
    }),
    new HumanMessage({
      content: `<task>Summarize the following conversation ${summarizationInput}</task>
<requirement>Only give the output, do not add any filler text.</requirement>`,
    }),
  ];
  const summaryResult = await model.invoke(summaryPrompt);
  const summaryMsg = new AIMessage({
    content: `[Summary of earlier chat]: ${summaryResult.content}`,
  });
  logger.debug(`summary: ${summaryMsg.content}`);
  // Build deletions for all the old messages we just summarized
  const deletions = toSummarize.map(
    (msg: BaseMessage) => new RemoveMessage({ id: msg.id as string }),
  );
  // And then append the summary + the last `threshold` raw messages
  const recent = messages.slice(-threshold);
  console.log(
    `🗑️ Deleting ${deletions.length} messages to stay under ${threshold} messages`,
  );
  return {
    messages: [...deletions, summaryMsg, ...recent],
  };
}

const trimmingStrategies = {
  top_k: topKMessageTrimmer,
  ai: aiMessageTrimmer,
  token: tokenMessageTrimmer,
};

export async function trimMessagesWithStrategy(
  messages: BaseMessage[],
  type: string,
  threshold: number,
  model?: any,
) {
  if (!trimmingStrategies[type]) {
    throw new Error(`Unknown trimming strategy: ${type}`);
  }

  const result = await trimmingStrategies[type](messages, threshold, model);

  // Ensure result has a valid messages property
  if (!result || typeof result !== 'object') {
    logger.warn(
      `Trimming strategy '${type}' returned invalid result, using empty messages array`,
    );
    return { messages: [] };
  }

  if (!Array.isArray(result.messages)) {
    logger.warn(
      `Trimming strategy '${type}' returned non-array messages, using empty messages array`,
    );
    return { messages: [] };
  }

  return result;
}
