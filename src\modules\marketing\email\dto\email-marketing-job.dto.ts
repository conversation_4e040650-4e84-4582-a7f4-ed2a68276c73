/**
 * DTO cho job email marketing trong queue
 */
export interface EmailMarketingJobDto {
  /**
   * ID của campaign
   */
  campaignId: number;

  /**
   * ID của audience cần gửi email
   */
  audienceId: number;

  /**
   * Email người nhận
   */
  email: string;

  /**
   * Tiêu đề email (có thể chứa biến {{variable}})
   */
  subject: string;

  /**
   * Nội dung email HTML (có thể chứa biến {{variable}})
   */
  content: string;

  /**
   * Dữ liệu biến tùy chỉnh để inject vào template
   */
  customFields: Record<string, any>;

  /**
   * Thông tin server gửi email (BẮT BUỘC cho email marketing)
   */
  server: {
    host: string;
    port?: number;
    secure?: boolean;
    user: string;
    password: string;
    from?: string;
  };

  /**
   * ID tracking duy nhất cho email này
   */
  trackingId: string;

  /**
   * Thời gian tạo job
   */
  createdAt: number;
}

/**
 * DTO cho recipient trong batch email job
 */
export interface EmailRecipientDto {
  /**
   * ID của audience nhận email
   */
  audienceId: number;

  /**
   * Email người nhận
   */
  email: string;
}

/**
 * DTO cho batch email marketing job (chứa nhiều email)
 */
export interface BatchEmailMarketingJobDto {
  /**
   * ID của campaign
   */
  campaignId: number;

  /**
   * ID của template email (worker sẽ lấy subject/content từ template)
   */
  templateId: number;

  /**
   * Template variables áp dụng cho tất cả recipients (worker sẽ kết hợp với custom fields của từng audience)
   */
  templateVariables: Record<string, any>;

  /**
   * Danh sách người nhận
   */
  recipients: EmailRecipientDto[];

  /**
   * Cấu hình SMTP server (optional)
   */
  server?: {
    host?: string;
    port?: number;
    secure?: boolean;
    user?: string;
    password?: string;
    from?: string;
  };

  /**
   * Timestamp tạo job
   */
  createdAt: number;
}

/**
 * Enum định nghĩa các tên job trong queue email marketing
 */
export enum EmailMarketingJobName {
  /**
   * Job gửi email marketing (single email)
   */
  SEND_EMAIL = 'send-email',

  /**
   * Job gửi batch email marketing (multiple emails)
   */
  SEND_BATCH_EMAIL = 'send-batch-email',
}
