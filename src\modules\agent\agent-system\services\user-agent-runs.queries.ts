import { Injectable, Logger } from '@nestjs/common';
import { ChatDatabaseService } from '../database.service';
import { UserAgentRunStatus } from '../../enums';

/**
 * Interface for user agent run data (agent perspective)
 */
export interface UserAgentRun {
  id: string;
  payload: any; // JSONB data - this is what the agent needs
  status: UserAgentRunStatus;
  created_at: number;
  created_by: number;
  thread_id: string; // Add thread_id field
}

/**
 * Raw SQL queries for user_agent_runs table operations (Agent Module)
 * 
 * The agent module only needs to:
 * 1. Query run by ID to get payload for processing
 * 2. Update run status during processing
 */
@Injectable()
export class UserAgentRunsQueries {
  constructor(private readonly databaseService: ChatDatabaseService) {}

  private readonly logger = new Logger(UserAgentRunsQueries.name);
  /**
   * Get a user agent run by ID (main agent operation)
   * @param id Run ID
   * @returns Promise<UserAgentRun | null>
   */
  async getRunById(id: string): Promise<UserAgentRun | null> {
    const query = `SELECT id, payload, status, created_at, created_by, thread_id
                   FROM user_agent_runs
                   WHERE id = $1`;

    const result = await this.databaseService.query(query, [id]);

    if (result.length === 0) {
      this.logger.debug(`no run found with id = ${id}`);
      return null;
    }

    this.logger.debug(`run found with id = ${id}`);
    return {
      id: result[0].id,
      payload: result[0].payload, // This is the SystemAgentConfig payload
      status: result[0].status as UserAgentRunStatus,
      created_at: result[0].created_at,
      created_by: result[0].created_by,
      thread_id: result[0].thread_id,
    };
  }

  /**
   * Update run status (agent updates status during processing)
   * @param id Run ID
   * @param status New status
   * @returns Promise<boolean> True if update was successful
   */
  async updateRunStatus(id: string, status: UserAgentRunStatus): Promise<boolean> {
    const query = `
      UPDATE user_agent_runs
      SET status = $1
      WHERE id = $2
    `;

    const result = await this.databaseService.query(query, [status, id]);
    return Array.isArray(result) && result.length >= 0;
  }

  /**
   * Find active run by threadId (replaces threadToRun map lookup)
   * @param threadId Thread ID to search for
   * @returns Promise<UserAgentRun | null> Active run or null if not found
   */
  async getActiveRunByThreadId(threadId: string): Promise<UserAgentRun | null> {
    // 🔍 DEBUG: Log thread ID details before query
    this.logger.debug(`🔍 LOOKING UP active run by threadId:`, {
      threadId,
      threadIdType: typeof threadId,
      threadIdLength: threadId?.length,
    });

    const query = `
      SELECT id, payload, status, created_at, created_by, thread_id
      FROM user_agent_runs
      WHERE thread_id = $1
      AND status = 'running'
      ORDER BY created_at DESC
      LIMIT 1
    `;

    const result = await this.databaseService.query(query, [threadId]);

    if (result.length === 0) {
      this.logger.debug(`No active run found for thread ${threadId}`);

      // 🔍 DEBUG: Check for similar thread IDs in database
      try {
        const similarQuery = `
          SELECT id, thread_id, status
          FROM user_agent_runs
          WHERE thread_id LIKE $1
          ORDER BY created_at DESC
          LIMIT 5
        `;
        const similarResults = await this.databaseService.query(similarQuery, [`%${threadId.substring(0, 8)}%`]);

        if (similarResults.length > 0) {
          this.logger.debug(`🔍 FOUND SIMILAR THREAD IDs in database:`, {
            searchedThreadId: threadId,
            similarThreads: similarResults.map(row => ({
              id: row.id,
              threadId: row.thread_id,
              status: row.status,
              isExactMatch: row.thread_id === threadId,
            })),
          });
        } else {
          this.logger.debug(`🔍 NO SIMILAR THREAD IDs found in database`);
        }
      } catch (error) {
        this.logger.error(`Error searching for similar thread IDs:`, error);
      }

      return null;
    }

    this.logger.debug(`✅ Found active run ${result[0].id} for thread ${threadId}`, {
      runId: result[0].id,
      threadId: result[0].thread_id,
      status: result[0].status,
      storedThreadIdLength: result[0].thread_id?.length,
      requestedThreadIdLength: threadId?.length,
      threadIdsMatch: result[0].thread_id === threadId,
    });

    return {
      id: result[0].id,
      payload: result[0].payload,
      status: result[0].status as UserAgentRunStatus,
      created_at: result[0].created_at,
      created_by: result[0].created_by,
      thread_id: result[0].thread_id,
    };
  }

  /**
   * Check if thread is currently active (replaces activeThreads.has())
   * @param threadId Thread ID to check
   * @returns Promise<boolean> True if thread has an active run
   */
  async isThreadActive(threadId: string): Promise<boolean> {
    const query = `
      SELECT 1 FROM user_agent_runs
      WHERE thread_id = $1
      AND status = 'running'
      LIMIT 1
    `;

    // 🔍 DEBUG: Log thread activity check
    this.logger.debug(`🔍 CHECKING thread activity:`, {
      threadId,
      threadIdType: typeof threadId,
      threadIdLength: threadId?.length,
    });

    const result = await this.databaseService.query(query, [threadId]);
    const isActive = result.length > 0;

    this.logger.debug(`📊 Thread activity result:`, {
      threadId,
      isActive,
      resultCount: result.length,
    });

    return isActive;
  }

  /**
   * Get runId from threadId (replaces threadToRun.get())
   * @param threadId Thread ID to lookup
   * @returns Promise<string | null> Run ID or null if not found
   */
  async getRunIdByThreadId(threadId: string): Promise<string | null> {
    const query = `
      SELECT id FROM user_agent_runs
      WHERE thread_id = $1
      AND status = 'running'
      ORDER BY created_at DESC
      LIMIT 1
    `;

    const result = await this.databaseService.query(query, [threadId]);
    return result.length > 0 ? result[0].id : null;
  }

  // ✅ REMOVED: cancelRun() method - use updateRunStatus(runId, UserAgentRunStatus.CANCELLED) instead
  // This eliminates duplicate status update logic and uses the standard updateRunStatus method
}
