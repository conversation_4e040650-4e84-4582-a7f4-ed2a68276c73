import { Modu<PERSON> } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { env } from './config';
import { QueueModule } from './queue';
import { InfraModule } from './infra';
import { SharedModule } from './shared/shared.module';
import Redis from 'ioredis';
import { TypeOrmModule } from '@nestjs/typeorm';
import { databaseConfig } from './config';
import { DatabaseModule } from './modules/database';
import { EmailSystemModule } from './modules/email-system/email-system.module';
import { EmailMarketingModule } from './modules/marketing/email/email-marketing.module';
import { SmsSystemModule } from './modules/sms_system/sms-system.module';
import { AgentModule } from './modules/agent';

@Module({
  imports: [
    SharedModule,
    QueueModule,
    InfraModule,
    AgentModule,
    DatabaseModule,
    EmailSystemModule,
    EmailMarketingModule,
    SmsSystemModule,
    TypeOrmModule.forRoot(databaseConfig),
    BullModule.forRoot({
      connection: {
        url: env.external.REDIS_URL,
      },
    }),
  ],
  providers: [
    {
      provide: Redis,
      useFactory: () => {
        return new Redis(env.external.REDIS_URL);
      },
    },
  ],
})
export class AppModule {}
