import {
  BaseMessage,
  isAIMessage,
  SystemMessage,
  ToolMessage,
} from '@langchain/core/messages';
import { RunnableConfig } from '@langchain/core/runnables';
import {
  BaseCheckpointSaver,
  BaseStore,
} from '@langchain/langgraph-checkpoint';
import {
  Annotation,
  AnnotationRoot,
  Command,
  CompiledStateGraph,
  END,
  interrupt,
  LangGraphRunnableConfig,
  Messages,
  messagesStateReducer,
  START,
  StateGraph,
} from '@langchain/langgraph';
import { InterruptShapeInterface, InterruptValue } from '../../interfaces';
import { SystemAgentConfig, SystemAgentConfigMap } from '../interfaces';
import { ModelProviderEnum } from '../../enums';
import { DynamicStructuredTool } from '@langchain/core/dist/tools';
import { MultiServerMCPClient } from '@langchain/mcp-adapters';
import { ToolNode } from '@langchain/langgraph/prebuilt';
import { Logger } from '@nestjs/common';
import { getHandoffTool } from './helpers';
import {
  SUPERVISOR_TAG,
  SUPERVISOR_TOOL_CALL_TAG,
  WORKER_TAG,
  WORKER_TOOL_CALL_TAG,
} from './constants';
import { initChatModel } from 'langchain/chat_models/universal';
import { backOff } from 'exponential-backoff';

const logger = new Logger('ReactAgentExecutor');

const CHAT_MODEL_WITHOUT_PARALLEL_TOOL_CALL_PARAM = new Set([
  'o1',
  'o1-2024-12-17',
  'o1-preview-2024-09-12',
  'o1-pro',
  'o1-pro-2025-03-19',
  'o3',
  'o3-2025-04-16',
  'o4-mini',
  'o4-mini-2025-04-16',
  'o3-mini',
  'o3-mini-2025-01-31',
  'o1-mini',
  'o1-mini-2024-09-12',
]);

export type N = typeof START | 'agent' | 'humanReview' | 'tools' | typeof END;

export function createReactAgentAnnotation() {
  return Annotation.Root({
    messages: Annotation<BaseMessage[], Messages>({
      reducer: messagesStateReducer,
      default: () => [],
    }),
    activeAgent: Annotation<string>({
      reducer: (x, y) => {
        logger.log(`transferring from ${x} to ${y}`);
        return y;
      },
      default: () => 'supervisor',
    }),
    metadata: Annotation<Record<string, any>>,
  });
}

export const GraphState = createReactAgentAnnotation();

export type AgentState = (typeof GraphState)['State'];

// Define custom configurable type for runtime configuration
export type CustomConfigurableType = {
  alwaysApproveToolCall?: boolean;
  thread_id?: string;
  agentConfigMap?: SystemAgentConfigMap;
  supervisorAgentId?: string;
  multiMcpClients?: MultiServerMCPClient;
};

// Define custom runnable config with our configurable type
export type CustomRunnableConfig = RunnableConfig<CustomConfigurableType>;

export type CreateReactAgentParams = {
  checkpointSaver?: BaseCheckpointSaver;
  store?: BaseStore;
  defaultTools?: DynamicStructuredTool[];
};

export function createReactAgent<
  A extends AnnotationRoot<any> = ReturnType<typeof createReactAgentAnnotation>,
>(
  params: CreateReactAgentParams,
): CompiledStateGraph<A['State'], A['Update'], any, A['spec'], A['spec']> {
  const { checkpointSaver, store } = params;

  const shouldContinue = (
    state: (typeof GraphState)['State'],
    config?: CustomRunnableConfig,
  ): N => {
    const { messages } = state;
    const lastMessage = messages[messages.length - 1];

    if (isAIMessage(lastMessage)) {
      const aiMessage = lastMessage;
      if (!aiMessage.tool_calls || aiMessage.tool_calls.length === 0) {
        return END;
      }

      const alwaysApproveToolCall =
        config?.configurable?.alwaysApproveToolCall || false;
      return alwaysApproveToolCall ? 'tools' : 'humanReview';
    }

    return END;
  };

  const humanReviewNode = (state: AgentState, config: CustomRunnableConfig) => {
    const { messages } = state;
    const lastMessage = messages[messages.length - 1];

    if (!lastMessage || !isAIMessage(lastMessage)) {
      throw new Error('Last message is not an AI message');
    }

    const aiMessage = lastMessage;

    // Check for errored tool calls (missing name or id)
    const erroredToolCalls =
      aiMessage.tool_calls?.filter(
        (toolCall) =>
          !toolCall.name ||
          toolCall.name.trim() === '' ||
          !toolCall.id ||
          toolCall.id.trim() === '',
      ) || [];

    if (erroredToolCalls.length > 0) {
      throw new Error(
        `Found ${erroredToolCalls.length} errored tool calls: ${JSON.stringify(erroredToolCalls)}. Tool calls must have both name and id.`,
      );
    }

    // Note: If alwaysApproveToolCall is true, we wouldn't reach this node
    // as shouldContinue would have routed directly to tools

    // Display the tool calls to the user and ask for approval
    const toolCalls =
      aiMessage.tool_calls
        ?.map((toolCall, index) => {
          return `Tool ${index + 1}: ${toolCall.name}\nArguments: ${JSON.stringify(toolCall.args, null, 2)}`;
        })
        .join('\n\n') || '';
    const name =
      state.activeAgent === config.configurable?.supervisorAgentId
        ? 'supervisor'
        : 'worker';
    const { choice } = interrupt<InterruptValue, InterruptShapeInterface>({
      role: name,
      prompt: `The AI wants to use the following tools:\n\n${toolCalls}\n\nDo you approve these tool calls? (yes/no/always)`,
      prompter:
        config?.configurable?.agentConfigMap?.[state.activeAgent]?.name || '',
      prompterId: state.activeAgent,
    });

    // Handle user response
    if (choice === 'always' || choice === 'yes') {
      // When user selects 'always', we just route to tools
      // The frontend should capture this response and set alwaysApproveToolCall=true in the configurable for future calls
      // We can't set configurable in the Command object
      return new Command({
        goto: 'tools',
      });
    } else {
      // User rejected the tool calls, create rejection tool messages
      const rejectionMessages =
        aiMessage.tool_calls?.map((toolCall) => {
          return new ToolMessage({
            content: 'Tool call was rejected by the user.',
            tool_call_id: toolCall.id ?? '',
            name: toolCall.name,
          });
        }) || [];

      // Return to the worker with the rejection messages
      return new Command({
        update: {
          messages: rejectionMessages,
        },
        goto: 'agent',
      });
    }
  };

  const callModel = async (
    state: typeof GraphState.State,
    config?: CustomRunnableConfig,
  ) => {
    // Get the active worker ID from state
    const activeAgentId = state.activeAgent;
    // Get the worker configuration from the config
    const agentConfig = config?.configurable?.agentConfigMap?.[activeAgentId];
    if (!agentConfig) {
      throw new Error(`No configuration found for agent: ${activeAgentId}`);
    }
    // Create a model instance based on the worker config
    const modelConfig = agentConfig.model;
    const apiKeys = modelConfig.apiKeys;
    // This is our state, scoped to this single `callModel` execution.
    let currentKeyIndex = 0;

    const apiCallTask = async () => {
      const apiKey = apiKeys[currentKeyIndex];
      const keyIdentifier = `...${apiKey.slice(-4)}`;
      logger.debug(
        `Attempting API call for agent ${activeAgentId} with key #${currentKeyIndex} (${keyIdentifier})`,
      );
      const dynamicLLM = await initChatModel(
        `${modelConfig.provider.toLowerCase()}:${modelConfig.name}`,
        {
          configurableFields: modelConfig.samplingParameters,
          ...modelConfig.parameters,
        },
      );
      // todo: modify _callTool
      const dynamicTools =
        (await config?.configurable?.multiMcpClients?.getTools(
          ...agentConfig.mcpConfig.map((mcp) => mcp.serverName),
        )) || [];
      const toolInstances = [...dynamicTools, ...(params.defaultTools || [])];
      const handoffTool = getHandoffTool(config);
      if (handoffTool) {
        toolInstances.push(handoffTool);
      }
      // Bind tools to the model
      // Check if the model is OpenAI to conditionally set parallel_tool_calls
      const condition =
        modelConfig.provider !== ModelProviderEnum.OPENAI ||
        CHAT_MODEL_WITHOUT_PARALLEL_TOOL_CALL_PARAM.has(modelConfig.name);

      const modelWithTools = condition
        ? dynamicLLM.bindTools(toolInstances)
        : dynamicLLM.bindTools(toolInstances, {
            parallel_tool_calls: false,
          });

      // Create a prompt runnable with the worker's system prompt
      const systemMessage = new SystemMessage(agentConfig.instruction);

      // Filter messages to only include those from the current worker
      const inputMessages = state.messages;

      const input = [systemMessage, ...inputMessages];

      const tag =
        agentConfig.id === config?.configurable?.supervisorAgentId
          ? SUPERVISOR_TAG
          : WORKER_TAG;
      // Invoke the model
      console.log(
        `---------------------------\nagent_id:${state.activeAgent}\n--------------------------`,
      );
      return await modelWithTools.invoke(input, {
        ...config,
        tags: [tag, `agent_id:${state.activeAgent}`],
      });
    };

    const response = await backOff(apiCallTask, {
      // We have exactly as many attempts as we have keys.
      numOfAttempts: apiKeys.length,
      startingDelay: 200, // A small delay between key swaps
      timeMultiple: 1.5, // You can adjust the backoff strategy

      // This function now controls the key swapping logic.
      retry: (e: any, attemptNumber: number) => {
        // if e is abort error we stop retrying
        if (e instanceof Error && e.name === 'AbortError') {
          logger.log('LangGraph stream aborted cleanly.');
          return false;
        }
        const keyIdentifier = `...${apiKeys[currentKeyIndex].slice(-4)}`;
        logger.warn(
          `Attempt #${attemptNumber} with key ${keyIdentifier} failed. Error: ${e.message}`,
        );

        // Advance to the next key for the next attempt.
        ++currentKeyIndex;

        // Check if we've run out of keys.
        if (currentKeyIndex >= apiKeys.length) {
          logger.error('All API keys have been tried and failed.');
          return false; // Stop retrying
        }

        logger.log(`Swapping to next key (index: ${currentKeyIndex}).`);
        return true; // Yes, please retry (the task will now use the new key)
      },
    });

    // Add worker name to the response
    response.response_metadata.invoker = activeAgentId;
    if (response.lc_kwargs) {
      response.lc_kwargs.name = `${activeAgentId}-${agentConfig.name}`;
    }

    return {
      messages: [response],
    };
  };

  const wrappedToolNode = async (
    state: AgentState,
    config?: LangGraphRunnableConfig,
  ) => {
    const { messages: prevMessages, activeAgent } = state;
    const lastMessage = prevMessages.at(-1);
    if (!lastMessage) {
      throw new Error('No messages found');
    }
    if (isAIMessage(lastMessage)) {
      const aiMessage = lastMessage;
      if (aiMessage.tool_calls && aiMessage.tool_calls.length > 0) {
        const erroredToolCalls = aiMessage.tool_calls.filter(
          (toolCall) =>
            !toolCall.name ||
            toolCall.name.trim() === '' ||
            !toolCall.id ||
            toolCall.id.trim() === '',
        );
        if (erroredToolCalls.length > 0) {
          throw new Error(
            `Found ${erroredToolCalls.length} errored tool calls: ${JSON.stringify(erroredToolCalls)}. Tool calls must have both name and id.`,
          );
        }
      }
    }

    const agentConfig = config?.configurable?.agentConfigMap?.[activeAgent];
    if (!agentConfig) {
      throw new Error(`No configuration found for agent: ${activeAgent}`);
    }
    const realAgentConfig = agentConfig as SystemAgentConfig;

    // todo: modify _callTool
    const dynamicTools =
      (await config?.configurable?.multiMcpClients?.getTools(
        ...agentConfig.mcpConfig.map((mcp) => mcp.serverName),
      )) || [];
    const toolInstances = [...dynamicTools, ...(params.defaultTools || [])];
    const handoffTool = getHandoffTool(config);
    if (handoffTool) {
      toolInstances.push(handoffTool);
    }

    const tag =
      realAgentConfig.id === config?.configurable?.supervisorAgentId
        ? SUPERVISOR_TOOL_CALL_TAG
        : WORKER_TOOL_CALL_TAG;
    // Create a dynamic tool node
    const dynamicToolNode = new ToolNode(toolInstances, {
      handleToolErrors: true,
    });
    const raw = await dynamicToolNode.invoke(state, {
      ...config,
      tags: [tag],
    });

    // Flatten raw into a single output array of either Commands or BaseMessage[]
    const output: any[] = [];
    if (Array.isArray(raw)) {
      logger.log('raw array');
      output.push(...raw);
    } else if (raw instanceof Command) {
      logger.log('raw command');
      output.push(raw);
    } else if (Array.isArray(raw.messages)) {
      logger.log('raw messages');
      output.push(...raw.messages);
    } else {
      logger.error('bad raw');
      throw new Error('wrappedToolNode: unexpected return shape');
    }
    const hasCommand = output.some((item) => item instanceof Command);
    if (hasCommand) {
      return output;
    }
    // Tag every BaseMessage with invoker
    const messages: BaseMessage[] = [];
    for (const item of output) {
      // item is either a BaseMessage or an object with `.messages`
      if (item instanceof BaseMessage) {
        item.response_metadata = {
          ...item.response_metadata,
          invoker: state.activeAgent,
        };
        messages.push(item);
      }
    }
    return { messages };
  };

  const workflow = new StateGraph(GraphState)
    .addNode('agent', callModel)
    .addNode('humanReview', humanReviewNode, { ends: ['tools', 'agent'] })
    .addNode('tools', wrappedToolNode)
    .addEdge(START, 'agent')
    .addConditionalEdges('agent', shouldContinue, {
      humanReview: 'humanReview',
      tools: 'tools',
      [END]: END,
    })
    .addEdge('tools', 'agent');
  return workflow.compile({
    checkpointer: checkpointSaver,
    store,
  });
}
