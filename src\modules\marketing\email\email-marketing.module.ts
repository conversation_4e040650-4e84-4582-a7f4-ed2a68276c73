import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { QueueName } from '../../../queue';
import { InfraModule } from '../../../infra';

// Entities
import { UserCampaign } from '../entities/user-campaign.entity';
import { UserAudience } from '../entities/user-audience.entity';
import { UserAudienceCustomField } from '../entities/user-audience-custom-field.entity';
import { UserCampaignHistory } from '../entities/user-campaign-history.entity';
import { UserTemplateEmail } from '../entities/user-template-email.entity';

// Services
import {
  EmailMarketingService,
  EmailTemplateService,
  EmailTrackingService,
} from './services';

// Processor
import { EmailMarketingProcessor } from './email-marketing.processor';

// Controllers
import { EmailTrackingController } from './email-tracking.controller';
import { EmailMarketingController } from './email-marketing.controller';

/**
 * Module xử lý email marketing
 */
@Module({
  imports: [
    // TypeORM entities
    TypeOrmModule.forFeature([
      UserCampaign,
      UserAudience,
      UserAudienceCustomField,
      UserCampaignHistory,
      UserTemplateEmail,
    ]),

    // Bull queue
    BullModule.registerQueue({
      name: QueueName.EMAIL_MARKETING,
    }),

    // Infrastructure module (Redis, etc.)
    InfraModule,
  ],
  providers: [
    EmailMarketingService,
    EmailTemplateService,
    EmailTrackingService,
    EmailMarketingProcessor,
  ],
  controllers: [EmailTrackingController, EmailMarketingController],
  exports: [EmailMarketingService, EmailTemplateService, EmailTrackingService],
})
export class EmailMarketingModule {}
