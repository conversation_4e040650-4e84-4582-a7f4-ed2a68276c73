import { Injectable, Logger } from '@nestjs/common';
import * as crypto from 'crypto';
import { env } from '../../../config';

/**
 * Service mã hóa nội dung SMS OTP
 */
@Injectable()
export class SmsEncryptionService {
  private readonly logger = new Logger(SmsEncryptionService.name);
  private readonly algorithm = 'aes-256-cbc';
  private readonly encoding: BufferEncoding = 'hex';
  private readonly textEncoding: crypto.Encoding = 'utf8';

  /**
   * Tạo key mã hóa từ secret key
   * @param secretKey Secret key từ environment
   * @returns Key đã được hash
   */
  private createKey(secretKey: string): string {
    return crypto.createHash('sha256').update(secretKey).digest('base64').substring(0, 32);
  }

  /**
   * Mã hóa nội dung SMS
   * @param content Nội dung SMS cần mã hóa
   * @param secretKey Secret key để mã hóa (optional, sử dụng default nếu không có)
   * @returns Nội dung đã được mã hóa (format: iv:encryptedData)
   */
  encryptSmsContent(content: string, secretKey?: string): string {
    try {
      // Sử dụng secret key từ environment hoặc key được truyền vào
      const key = this.createKey(secretKey || this.getDefaultSecretKey());
      
      // Tạo IV ngẫu nhiên
      const iv = crypto.randomBytes(16);
      
      // Tạo cipher
      const cipher = crypto.createCipheriv(this.algorithm, key, iv);
      
      // Mã hóa
      let encrypted = cipher.update(content, this.textEncoding, this.encoding);
      encrypted += cipher.final(this.encoding);
      
      // Trả về format: iv:encryptedData
      const result = `${iv.toString(this.encoding)}:${encrypted}`;
      
      this.logger.debug(`Đã mã hóa nội dung SMS: ${content.substring(0, 20)}...`);
      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi mã hóa nội dung SMS: ${error.message}`, error.stack);
      throw new Error(`Không thể mã hóa nội dung SMS: ${error.message}`);
    }
  }

  /**
   * Giải mã nội dung SMS
   * @param encryptedContent Nội dung đã được mã hóa (format: iv:encryptedData)
   * @param secretKey Secret key để giải mã (optional, sử dụng default nếu không có)
   * @returns Nội dung gốc đã được giải mã
   */
  decryptSmsContent(encryptedContent: string, secretKey?: string): string {
    try {
      // Sử dụng secret key từ environment hoặc key được truyền vào
      const key = this.createKey(secretKey || this.getDefaultSecretKey());
      
      // Tách IV và dữ liệu đã mã hóa
      const textParts = encryptedContent.split(':');
      if (textParts.length !== 2) {
        throw new Error('Format nội dung mã hóa không hợp lệ');
      }
      
      const iv = Buffer.from(textParts[0], this.encoding);
      const encryptedData = textParts[1];
      
      // Tạo decipher
      const decipher = crypto.createDecipheriv(this.algorithm, key, iv);
      
      // Giải mã
      let decrypted = decipher.update(encryptedData, this.encoding, this.textEncoding);
      decrypted += decipher.final(this.textEncoding);
      
      this.logger.debug(`Đã giải mã nội dung SMS thành công`);
      return decrypted;
    } catch (error) {
      this.logger.error(`Lỗi khi giải mã nội dung SMS: ${error.message}`, error.stack);
      throw new Error(`Không thể giải mã nội dung SMS: ${error.message}`);
    }
  }

  /**
   * Mã hóa nội dung OTP với format đặc biệt
   * @param otpContent Nội dung OTP cần mã hóa
   * @param userId ID người dùng (để tạo key riêng biệt)
   * @returns Nội dung OTP đã được mã hóa
   */
  encryptOtpContent(otpContent: string, userId?: number): string {
    try {
      // Tạo secret key riêng cho từng user nếu có userId
      const secretKey = userId 
        ? `${this.getDefaultSecretKey()}_${userId}` 
        : this.getDefaultSecretKey();
      
      const encryptedContent = this.encryptSmsContent(otpContent, secretKey);
      
      this.logger.log(`Đã mã hóa nội dung OTP${userId ? ` cho user ${userId}` : ''}`);
      return encryptedContent;
    } catch (error) {
      this.logger.error(`Lỗi khi mã hóa OTP: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Giải mã nội dung OTP
   * @param encryptedOtpContent Nội dung OTP đã được mã hóa
   * @param userId ID người dùng (để tạo key riêng biệt)
   * @returns Nội dung OTP gốc
   */
  decryptOtpContent(encryptedOtpContent: string, userId?: number): string {
    try {
      // Tạo secret key riêng cho từng user nếu có userId
      const secretKey = userId 
        ? `${this.getDefaultSecretKey()}_${userId}` 
        : this.getDefaultSecretKey();
      
      const decryptedContent = this.decryptSmsContent(encryptedOtpContent, secretKey);
      
      this.logger.log(`Đã giải mã nội dung OTP${userId ? ` cho user ${userId}` : ''}`);
      return decryptedContent;
    } catch (error) {
      this.logger.error(`Lỗi khi giải mã OTP: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy default secret key từ environment
   * @returns Default secret key
   */
  private getDefaultSecretKey(): string {
    // Sử dụng ENCRYPTION_SECRET_KEY từ .env
    const secretKey = process.env.ENCRYPTION_SECRET_KEY || env.agent?.ENCRYPTION_SECRET_KEY;

    if (!secretKey) {
      throw new Error('ENCRYPTION_SECRET_KEY không được cấu hình trong environment variables');
    }

    return secretKey;
  }

  /**
   * Kiểm tra xem nội dung có được mã hóa hay không
   * @param content Nội dung cần kiểm tra
   * @returns true nếu nội dung đã được mã hóa
   */
  isEncrypted(content: string): boolean {
    // Kiểm tra format iv:encryptedData
    const parts = content.split(':');
    if (parts.length !== 2) {
      return false;
    }
    
    try {
      // Thử parse IV để xem có hợp lệ không
      Buffer.from(parts[0], this.encoding);
      return true;
    } catch {
      return false;
    }
  }
}
