import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdminTemplateSms } from '../entities/admin-template-sms.entity';
import { SmsTypeEnum } from '../constants';

/**
 * Service xử lý template SMS
 */
@Injectable()
export class SmsTemplateService {
  private readonly logger = new Logger(SmsTemplateService.name);

  constructor(
    @InjectRepository(AdminTemplateSms)
    private readonly adminTemplateSmsRepository: Repository<AdminTemplateSms>,
  ) {}

  /**
   * Lấy template SMS theo loại
   * @param type Loại SMS
   * @returns Template SMS
   */
  async getTemplateByType(type: SmsTypeEnum): Promise<AdminTemplateSms> {
    this.logger.debug(`Lấy template SMS cho loại: ${type}`);

    const template = await this.adminTemplateSmsRepository.findOne({
      where: { category: type },
    });

    if (!template) {
      this.logger.error(`Không tìm thấy template SMS cho loại: ${type}`);
      throw new NotFoundException(`Không tìm thấy template SMS cho loại: ${type}`);
    }

    this.logger.debug(`Đã tìm thấy template SMS: ${template.name}`);
    return template;
  }

  /**
   * Thay thế các placeholder trong nội dung SMS
   * @param content Nội dung SMS gốc
   * @param data Dữ liệu để thay thế
   * @returns Nội dung SMS đã được thay thế
   */
  replaceTemplateVariables(content: string, data: Record<string, any>): string {
    this.logger.debug('Thay thế biến trong template SMS');

    let processedContent = content;

    // Thay thế các biến có cú pháp {{BIEN}}
    Object.keys(data).forEach((key) => {
      const placeholder = `{{${key.toUpperCase()}}}`;
      const value = data[key]?.toString() || '';
      processedContent = processedContent.replace(new RegExp(placeholder, 'g'), value);
      
      this.logger.debug(`Thay thế ${placeholder} -> ${value}`);
    });

    this.logger.debug(`Nội dung SMS sau khi thay thế: ${processedContent}`);
    return processedContent;
  }

  /**
   * Xử lý template SMS hoàn chỉnh
   * @param type Loại SMS
   * @param data Dữ liệu để thay thế
   * @returns Nội dung SMS đã được xử lý
   */
  async processTemplate(type: SmsTypeEnum, data: Record<string, any>): Promise<string> {
    this.logger.log(`Xử lý template SMS cho loại: ${type}`);

    // Lấy template từ database
    const template = await this.getTemplateByType(type);

    // Thay thế các biến trong template
    const processedContent = this.replaceTemplateVariables(template.content, data);

    this.logger.log(`Đã xử lý template SMS thành công cho loại: ${type}`);
    return processedContent;
  }
}
