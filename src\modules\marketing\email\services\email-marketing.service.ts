import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { UserCampaign } from '../../entities/user-campaign.entity';
import { UserAudience } from '../../entities/user-audience.entity';
import { UserAudienceCustomField } from '../../entities/user-audience-custom-field.entity';
import { UserTemplateEmail } from '../../entities/user-template-email.entity';
import { EmailMarketingJobDto, EmailMarketingJobName } from '../dto';
import { EmailTrackingService } from './email-tracking.service';
import { QueueName } from '../../../../queue';

/**
 * Service chính xử lý email marketing
 */
@Injectable()
export class EmailMarketingService {
  private readonly logger = new Logger(EmailMarketingService.name);

  constructor(
    @InjectRepository(UserCampaign)
    private readonly campaignRepository: Repository<UserCampaign>,
    @InjectRepository(UserAudience)
    private readonly audienceRepository: Repository<UserAudience>,
    @InjectRepository(UserAudienceCustomField)
    private readonly customFieldRepository: Repository<UserAudienceCustomField>,
    @InjectRepository(UserTemplateEmail)
    private readonly templateRepository: Repository<UserTemplateEmail>,
    @InjectQueue(QueueName.EMAIL_MARKETING)
    private readonly emailMarketingQueue: Queue,
    private readonly emailTrackingService: EmailTrackingService,
  ) {}

  /**
   * Tạo jobs email marketing cho một campaign
   * @param campaignId ID của campaign
   * @returns Số lượng jobs đã tạo
   */
  async createEmailMarketingJobs(campaignId: number): Promise<number> {
    try {
      this.logger.log(
        `Creating email marketing jobs for campaign: ${campaignId}`,
      );

      // Lấy thông tin campaign
      const campaign = await this.campaignRepository.findOne({
        where: { id: campaignId },
      });

      if (!campaign) {
        throw new Error(`Campaign not found: ${campaignId}`);
      }

      if (campaign.platform !== 'email') {
        throw new Error(`Campaign ${campaignId} is not an email campaign`);
      }

      // Validate server config - BẮT BUỘC cho email marketing
      if (!campaign.server) {
        throw new Error(`Campaign ${campaignId} missing server configuration`);
      }

      if (!campaign.server.host || !campaign.server.user || !campaign.server.password) {
        throw new Error(`Campaign ${campaignId} has incomplete server configuration`);
      }

      // Lấy danh sách audience IDs
      let audienceIds: number[] = [];

      if (campaign.audienceIds && Array.isArray(campaign.audienceIds)) {
        audienceIds = campaign.audienceIds;
      } else if (campaign.segmentId) {
        // TODO: Implement segment logic if needed
        this.logger.warn(
          `Segment logic not implemented for campaign ${campaignId}`,
        );
        return 0;
      } else {
        this.logger.warn(
          `No audience or segment found for campaign ${campaignId}`,
        );
        return 0;
      }

      // Lấy thông tin audiences
      const audiences = await this.audienceRepository
        .createQueryBuilder('audience')
        .where('audience.id IN (:...ids)', { ids: audienceIds })
        .getMany();

      if (audiences.length === 0) {
        this.logger.warn(`No valid audiences found for campaign ${campaignId}`);
        return 0;
      }

      let jobCount = 0;

      // Tạo job cho từng audience
      for (const audience of audiences) {
        if (!audience.email) {
          this.logger.warn(`Audience ${audience.id} has no email, skipping`);
          continue;
        }

        try {
          // Lấy custom fields cho audience này
          const customFields = await this.getAudienceCustomFields(audience.id);

          // Tạo tracking ID
          const trackingId = this.emailTrackingService.generateTrackingId(
            campaignId,
            audience.id,
          );

          // Tạo job data (server đã được validate ở trên)
          const jobData: EmailMarketingJobDto = {
            campaignId: campaign.id,
            audienceId: audience.id,
            email: audience.email,
            subject: campaign.subject || '',
            content: campaign.content || '',
            customFields,
            server: campaign.server!, // Non-null assertion vì đã validate
            trackingId,
            createdAt: Date.now(),
          };

          // Thêm job vào queue
          await this.emailMarketingQueue.add(EmailMarketingJobName.SEND_EMAIL, jobData, {
            delay: campaign.scheduledAt
              ? Math.max(0, campaign.scheduledAt - Date.now())
              : 0,
            attempts: 3,
            backoff: {
              type: 'exponential',
              delay: 2000,
            },
          });

          jobCount++;
          this.logger.debug(
            `Created job for audience ${audience.id} (${audience.email})`,
          );
        } catch (error) {
          this.logger.error(
            `Error creating job for audience ${audience.id}: ${error.message}`,
          );
        }
      }

      this.logger.log(
        `Created ${jobCount} email marketing jobs for campaign ${campaignId}`,
      );
      return jobCount;
    } catch (error) {
      this.logger.error(
        `Error creating email marketing jobs: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy template email theo ID
   * @param templateId ID của template
   * @returns Template email hoặc null nếu không tìm thấy
   */
  async getTemplateById(templateId: number): Promise<UserTemplateEmail | null> {
    try {
      const template = await this.templateRepository.findOne({
        where: { id: templateId },
      });

      if (!template) {
        this.logger.warn(`Template not found: ${templateId}`);
        return null;
      }

      return template;
    } catch (error) {
      this.logger.error(
        `Error getting template ${templateId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy custom fields cho một audience
   * @param audienceId ID của audience
   * @returns Object chứa custom fields
   */
  private async getAudienceCustomFields(
    audienceId: number,
  ): Promise<Record<string, any>> {
    try {
      const customFields = await this.customFieldRepository.find({
        where: { audienceId },
      });

      const result: Record<string, any> = {};

      for (const field of customFields) {
        if (field.fieldName) {
          result[field.fieldName] = field.fieldValue;
        }
      }

      return result;
    } catch (error) {
      this.logger.error(
        `Error getting custom fields for audience ${audienceId}: ${error.message}`,
      );
      return {};
    }
  }

  /**
   * Lấy thống kê campaign
   * @param campaignId ID của campaign
   * @returns Thống kê campaign
   */
  async getCampaignStats(campaignId: number): Promise<any> {
    try {
      // TODO: Implement campaign statistics
      // Có thể lấy từ UserCampaignHistory để tính toán các metrics
      return {
        campaignId,
        totalSent: 0,
        totalOpened: 0,
        totalClicked: 0,
        totalBounced: 0,
        totalFailed: 0,
      };
    } catch (error) {
      this.logger.error(
        `Error getting campaign stats: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Hủy tất cả jobs của một campaign
   * @param campaignId ID của campaign
   * @returns Số lượng jobs đã hủy
   */
  async cancelCampaignJobs(campaignId: number): Promise<number> {
    try {
      this.logger.log(`Canceling jobs for campaign: ${campaignId}`);

      // Lấy tất cả jobs đang chờ
      const jobs = await this.emailMarketingQueue.getJobs([
        'waiting',
        'delayed',
      ]);

      let canceledCount = 0;

      for (const job of jobs) {
        const jobData = job.data as EmailMarketingJobDto;
        if (jobData.campaignId === campaignId) {
          await job.remove();
          canceledCount++;
        }
      }

      this.logger.log(
        `Canceled ${canceledCount} jobs for campaign ${campaignId}`,
      );
      return canceledCount;
    } catch (error) {
      this.logger.error(
        `Error canceling campaign jobs: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy trạng thái queue
   * @returns Thông tin trạng thái queue
   */
  async getQueueStatus(): Promise<any> {
    try {
      const waiting = await this.emailMarketingQueue.getWaiting();
      const active = await this.emailMarketingQueue.getActive();
      const completed = await this.emailMarketingQueue.getCompleted();
      const failed = await this.emailMarketingQueue.getFailed();
      const delayed = await this.emailMarketingQueue.getDelayed();

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length,
      };
    } catch (error) {
      this.logger.error(
        `Error getting queue status: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
