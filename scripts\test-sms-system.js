const { execSync } = require('child_process');

console.log('🚀 Bắt đầu test SMS System...\n');

try {
  // 1. Build project
  console.log('📦 Building project...');
  execSync('npm run build', { stdio: 'inherit' });
  console.log('✅ Build thành công!\n');

  // 2. Seed SMS templates
  console.log('🌱 Seeding SMS templates...');
  execSync('npx ts-node src/modules/sms_system/scripts/seed-sms-templates.ts', { stdio: 'inherit' });
  console.log('✅ Seed templates thành công!\n');

  // 3. Test SMS system
  console.log('🧪 Testing SMS system...');
  execSync('npx ts-node src/modules/sms_system/test-sms.ts', { stdio: 'inherit' });
  console.log('✅ Test SMS system thành công!\n');

  console.log('🎉 Tất cả test đều thành công!');
  console.log('\n📋 <PERSON><PERSON><PERSON> bước tiếp theo:');
  console.log('1. Ki<PERSON>m tra queue dashboard tại: http://localhost:3000/queues');
  console.log('2. <PERSON><PERSON><PERSON> tra logs để xem job được xử lý');
  console.log('3. Kiểm tra database để xem templates đã được tạo');

} catch (error) {
  console.error('❌ Lỗi khi test SMS system:', error.message);
  process.exit(1);
}
