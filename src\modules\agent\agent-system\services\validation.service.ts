import { Injectable, Logger } from '@nestjs/common';
import { z } from 'zod';
import {
  SystemAgentConfigSchema,
  SystemAgentConfigMapSchema,
  CustomConfigurableTypeSchema,
  ThreadConfigurationSchema,
  StreamingComponentsSchema,
  EventProcessingContextSchema,
  ProcessingResultSchema,

  CompletionResultSchema,
} from '../schemas';
import type {
  SystemAgentConfig,
  SystemAgentConfigMap,
  CustomConfigurableType,
  ThreadConfiguration,
  StreamingComponents,
  EventProcessingContext,
  ProcessingResult,
  CompletionResult,
} from '../interfaces';


/**
 * Validation result interface
 */
export interface ValidationResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  issues?: z.ZodIssue[];
}

/**
 * Service for validating agent-system data using Zod schemas
 */
@Injectable()
export class ValidationService {
  private readonly logger = new Logger(ValidationService.name);

  /**
   * Validate system agent configuration
   */
  validateSystemAgentConfig(data: unknown): ValidationResult<SystemAgentConfig> {
    try {
      const result = SystemAgentConfigSchema.safeParse(data);
      
      if (result.success) {
        this.logger.debug('System agent config validation successful', {
          agentId: result.data.id,
          agentName: result.data.name,
        });
        return { success: true, data: result.data };
      } else {
        this.logger.warn('System agent config validation failed', {
          issues: result.error.issues,
        });
        return {
          success: false,
          error: 'System agent configuration validation failed',
          issues: result.error.issues,
        };
      }
    } catch (error) {
      this.logger.error('Unexpected error during system agent config validation', error);
      return {
        success: false,
        error: `Validation error: ${error.message}`,
      };
    }
  }

  /**
   * Validate system agent configuration map
   */
  validateSystemAgentConfigMap(data: unknown): ValidationResult<SystemAgentConfigMap> {
    try {
      const result = SystemAgentConfigMapSchema.safeParse(data);
      
      if (result.success) {
        const agentCount = Object.keys(result.data).length;
        this.logger.debug('System agent config map validation successful', {
          agentCount,
          agentIds: Object.keys(result.data),
        });
        return { success: true, data: result.data };
      } else {
        this.logger.warn('System agent config map validation failed', {
          issues: result.error.issues,
        });
        return {
          success: false,
          error: 'System agent configuration map validation failed',
          issues: result.error.issues,
        };
      }
    } catch (error) {
      this.logger.error('Unexpected error during system agent config map validation', error);
      return {
        success: false,
        error: `Validation error: ${error.message}`,
      };
    }
  }

  /**
   * Validate custom configurable type
   */
  validateCustomConfigurable(data: unknown): ValidationResult<CustomConfigurableType> {
    try {
      const result = CustomConfigurableTypeSchema.safeParse(data);
      
      if (result.success) {
        this.logger.debug('Custom configurable validation successful', {
          threadId: result.data.thread_id,
          supervisorAgentId: result.data.supervisorAgentId,
          hasAgentConfigMap: !!result.data.agentConfigMap,
        });
        return { success: true, data: result.data };
      } else {
        this.logger.warn('Custom configurable validation failed', {
          issues: result.error.issues,
        });
        return {
          success: false,
          error: 'Custom configurable validation failed',
          issues: result.error.issues,
        };
      }
    } catch (error) {
      this.logger.error('Unexpected error during custom configurable validation', error);
      return {
        success: false,
        error: `Validation error: ${error.message}`,
      };
    }
  }

  /**
   * Validate thread configuration
   */
  validateThreadConfiguration(data: unknown): ValidationResult<ThreadConfiguration> {
    try {
      const result = ThreadConfigurationSchema.safeParse(data);
      
      if (result.success) {
        this.logger.debug('Thread configuration validation successful', {
          threadId: result.data.threadId,
          userId: result.data.userId,
        });
        return { success: true, data: result.data };
      } else {
        this.logger.warn('Thread configuration validation failed', {
          issues: result.error.issues,
        });
        return {
          success: false,
          error: 'Thread configuration validation failed',
          issues: result.error.issues,
        };
      }
    } catch (error) {
      this.logger.error('Unexpected error during thread configuration validation', error);
      return {
        success: false,
        error: `Validation error: ${error.message}`,
      };
    }
  }

  /**
   * Validate streaming components
   */
  validateStreamingComponents(data: unknown): ValidationResult<StreamingComponents> {
    try {
      const result = StreamingComponentsSchema.safeParse(data);
      
      if (result.success) {
        this.logger.debug('Streaming components validation successful', {
          partialTokensCount: result.data.partialTokens.length,
        });
        return { success: true, data: result.data };
      } else {
        this.logger.warn('Streaming components validation failed', {
          issues: result.error.issues,
        });
        return {
          success: false,
          error: 'Streaming components validation failed',
          issues: result.error.issues,
        };
      }
    } catch (error) {
      this.logger.error('Unexpected error during streaming components validation', error);
      return {
        success: false,
        error: `Validation error: ${error.message}`,
      };
    }
  }

  /**
   * Validate event processing context
   */
  validateEventProcessingContext(data: unknown): ValidationResult<EventProcessingContext> {
    try {
      const result = EventProcessingContextSchema.safeParse(data);
      
      if (result.success) {
        this.logger.debug('Event processing context validation successful', {
          event: result.data.event,
          threadId: result.data.threadId,
          tagsCount: result.data.tags.length,
        });
        return { success: true, data: result.data };
      } else {
        this.logger.warn('Event processing context validation failed', {
          issues: result.error.issues,
        });
        return {
          success: false,
          error: 'Event processing context validation failed',
          issues: result.error.issues,
        };
      }
    } catch (error) {
      this.logger.error('Unexpected error during event processing context validation', error);
      return {
        success: false,
        error: `Validation error: ${error.message}`,
      };
    }
  }

  /**
   * Validate processing result
   */
  validateProcessingResult(data: unknown): ValidationResult<ProcessingResult> {
    try {
      const result = ProcessingResultSchema.safeParse(data);
      
      if (result.success) {
        this.logger.debug('Processing result validation successful', {
          completed: result.data.completed,
          cancelled: result.data.cancelled,
          hasError: !!result.data.error,
        });
        return { success: true, data: result.data };
      } else {
        this.logger.warn('Processing result validation failed', {
          issues: result.error.issues,
        });
        return {
          success: false,
          error: 'Processing result validation failed',
          issues: result.error.issues,
        };
      }
    } catch (error) {
      this.logger.error('Unexpected error during processing result validation', error);
      return {
        success: false,
        error: `Validation error: ${error.message}`,
      };
    }
  }



  /**
   * Validate completion result
   */
  validateCompletionResult(data: unknown): ValidationResult<CompletionResult> {
    try {
      const result = CompletionResultSchema.safeParse(data);
      
      if (result.success) {
        this.logger.debug('Completion result validation successful', {
          success: result.data.success,
          threadId: result.data.threadId,
          runId: result.data.runId,
        });
        return { success: true, data: result.data };
      } else {
        this.logger.warn('Completion result validation failed', {
          issues: result.error.issues,
        });
        return {
          success: false,
          error: 'Completion result validation failed',
          issues: result.error.issues,
        };
      }
    } catch (error) {
      this.logger.error('Unexpected error during completion result validation', error);
      return {
        success: false,
        error: `Validation error: ${error.message}`,
      };
    }
  }

  /**
   * Helper method to format validation errors for user-friendly display
   */
  formatValidationErrors(issues: z.ZodIssue[]): string {
    return issues
      .map(issue => `${issue.path.join('.')}: ${issue.message}`)
      .join('; ');
  }

  /**
   * Helper method to check if data matches a specific schema without full validation
   */
  isValidShape<T>(data: unknown, schema: z.ZodSchema<T>): data is T {
    return schema.safeParse(data).success;
  }
}
